#!/usr/bin/env node

/**
 * 测试池子查询功能
 * 
 * 测试mint地址对应的真实池子查询
 */

const { PoolQueryService } = require('./dist/pool-query-service.js');

async function testPoolQuery() {
  console.log('🧪 测试池子查询功能...\n');

  const poolQueryService = new PoolQueryService('https://api.mainnet-beta.solana.com');

  // 测试mint地址
  const testMint = '39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73';
  
  console.log(`🔍 查询mint: ${testMint}`);
  console.log('期望找到的池子:');
  console.log('  - BdjfXqH2MDPhuoaMUgKPy4RwF6sStUb8nDqjrJGgDUaC');
  console.log('  - JDfEHyw1sGWNrRGvjPE5uFKatPkyVViRcaveVp1FDcDc');
  console.log('');

  try {
    // 查询池子
    const pools = await poolQueryService.queryPoolsByMint(testMint);
    
    console.log(`✅ 查询完成，找到 ${pools.length} 个池子:`);
    
    if (pools.length > 0) {
      pools.forEach((pool, index) => {
        console.log(`\n${index + 1}. ${pool.dex} Pool:`);
        console.log(`   地址: ${pool.poolAddress}`);
        console.log(`   类型: ${pool.poolType}`);
        console.log(`   TokenA: ${pool.tokenA}`);
        console.log(`   TokenB: ${pool.tokenB}`);
        if (pool.additionalInfo) {
          console.log(`   描述: ${pool.additionalInfo.description || pool.additionalInfo.marketType}`);
        }
        if (pool.tvl) {
          console.log(`   TVL: $${pool.tvl.toLocaleString()}`);
        }
      });
    } else {
      console.log('❌ 未找到任何池子');
      
      // 尝试DexScreener查询
      console.log('\n🔄 尝试通过DexScreener查询...');
      const dexScreenerPools = await poolQueryService.queryDexScreenerPools(testMint);
      
      if (dexScreenerPools.length > 0) {
        console.log(`✅ DexScreener找到 ${dexScreenerPools.length} 个池子:`);
        dexScreenerPools.forEach((pool, index) => {
          console.log(`\n${index + 1}. ${pool.dex} Pool:`);
          console.log(`   地址: ${pool.poolAddress}`);
          console.log(`   描述: ${pool.additionalInfo?.description}`);
          if (pool.tvl) {
            console.log(`   TVL: $${pool.tvl.toLocaleString()}`);
          }
          if (pool.volume24h) {
            console.log(`   24h交易量: $${pool.volume24h.toLocaleString()}`);
          }
        });
      } else {
        console.log('❌ DexScreener也未找到池子');
      }
    }

    console.log('\n🎯 验证结果:');
    const expectedPools = [
      'BdjfXqH2MDPhuoaMUgKPy4RwF6sStUb8nDqjrJGgDUaC',
      'JDfEHyw1sGWNrRGvjPE5uFKatPkyVViRcaveVp1FDcDc'
    ];
    
    let foundExpected = 0;
    for (const expectedPool of expectedPools) {
      const found = pools.some(pool => pool.poolAddress === expectedPool);
      if (found) {
        console.log(`✅ 找到期望的池子: ${expectedPool}`);
        foundExpected++;
      } else {
        console.log(`❌ 未找到期望的池子: ${expectedPool}`);
      }
    }
    
    console.log(`\n📊 总结: 找到 ${foundExpected}/${expectedPools.length} 个期望的池子`);

  } catch (error) {
    console.error('❌ 查询失败:', error.message);
  }
}

// 测试其他mint
async function testMultipleMints() {
  console.log('\n🔄 测试多个mint地址...\n');
  
  const poolQueryService = new PoolQueryService('https://api.mainnet-beta.solana.com');
  
  const testMints = [
    '39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73', // 你提供的mint
    'So11111111111111111111111111111111111111112',   // SOL
    'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'    // USDC
  ];

  for (const mint of testMints) {
    console.log(`🔍 查询 ${mint.slice(0, 8)}...${mint.slice(-8)}`);
    
    try {
      const pools = await poolQueryService.queryPoolsByMint(mint);
      console.log(`   找到 ${pools.length} 个池子`);
      
      if (pools.length > 0) {
        const dexTypes = [...new Set(pools.map(p => p.dex))];
        console.log(`   DEX: ${dexTypes.join(', ')}`);
      }
    } catch (error) {
      console.log(`   查询失败: ${error.message}`);
    }
    
    console.log('');
  }
}

async function main() {
  await testPoolQuery();
  await testMultipleMints();
  
  console.log('🎉 池子查询测试完成！');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testPoolQuery };
