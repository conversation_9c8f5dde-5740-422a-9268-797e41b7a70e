#!/usr/bin/env node

/**
 * 调试交易解析问题
 */

const { Connection } = require('@solana/web3.js');

const RPC_URL = 'https://api.mainnet-beta.solana.com';
const TEST_TRANSACTION = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';

async function debugTransaction() {
  console.log('🔍 调试交易解析问题');
  console.log(`📝 交易: ${TEST_TRANSACTION}`);
  console.log('');

  try {
    const connection = new Connection(RPC_URL);
    
    console.log('📡 尝试获取交易...');
    
    // 尝试不同的方法获取交易
    console.log('方法1: getParsedTransaction');
    const parsedTx = await connection.getParsedTransaction(TEST_TRANSACTION, {
      maxSupportedTransactionVersion: 0
    });
    
    if (parsedTx) {
      console.log('✅ getParsedTransaction 成功');
      console.log(`   插槽: ${parsedTx.slot}`);
      console.log(`   区块时间: ${parsedTx.blockTime}`);
      console.log(`   账户数: ${parsedTx.transaction.message.accountKeys.length}`);
    } else {
      console.log('❌ getParsedTransaction 返回null');
    }

    console.log('\n方法2: getTransaction');
    const rawTx = await connection.getTransaction(TEST_TRANSACTION, {
      maxSupportedTransactionVersion: 0
    });
    
    if (rawTx) {
      console.log('✅ getTransaction 成功');
      console.log(`   插槽: ${rawTx.slot}`);
      console.log(`   区块时间: ${rawTx.blockTime}`);
    } else {
      console.log('❌ getTransaction 返回null');
    }

    // 检查交易是否存在
    console.log('\n方法3: getSignatureStatus');
    const status = await connection.getSignatureStatus(TEST_TRANSACTION);
    
    if (status && status.value) {
      console.log('✅ 交易状态查询成功');
      console.log(`   确认状态: ${status.value.confirmationStatus}`);
      console.log(`   插槽: ${status.value.slot}`);
      console.log(`   错误: ${status.value.err || '无'}`);
    } else {
      console.log('❌ 交易状态查询失败');
    }

    // 如果交易存在，分析为什么解析失败
    if (parsedTx || rawTx) {
      console.log('\n🔍 交易存在，分析解析失败原因...');
      
      const tx = parsedTx || rawTx;
      
      // 检查基本结构
      console.log('交易结构检查:');
      console.log(`   transaction: ${!!tx.transaction}`);
      console.log(`   meta: ${!!tx.meta}`);
      console.log(`   slot: ${tx.slot}`);
      console.log(`   blockTime: ${tx.blockTime}`);
      
      if (tx.transaction) {
        console.log(`   message: ${!!tx.transaction.message}`);
        if (tx.transaction.message) {
          console.log(`   accountKeys: ${!!tx.transaction.message.accountKeys}`);
          console.log(`   instructions: ${!!tx.transaction.message.instructions}`);
          if (tx.transaction.message.accountKeys) {
            console.log(`   accountKeys长度: ${tx.transaction.message.accountKeys.length}`);
          }
          if (tx.transaction.message.instructions) {
            console.log(`   instructions长度: ${tx.transaction.message.instructions.length}`);
          }
        }
      }
      
      if (tx.meta) {
        console.log(`   logMessages: ${!!tx.meta.logMessages}`);
        console.log(`   preTokenBalances: ${!!tx.meta.preTokenBalances}`);
        console.log(`   postTokenBalances: ${!!tx.meta.postTokenBalances}`);
        if (tx.meta.logMessages) {
          console.log(`   logMessages长度: ${tx.meta.logMessages.length}`);
        }
      }
    }

  } catch (error) {
    console.error('❌ 调试失败:', error.message);
    console.error(error.stack);
  }
}

// 运行调试
if (require.main === module) {
  debugTransaction().catch(console.error);
}

module.exports = { debugTransaction };
