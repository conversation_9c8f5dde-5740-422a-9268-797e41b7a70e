# MEV交易监听器

## 🎯 功能概述

这个MEV监听器可以：
- ✅ **实时监听**包含MEV合约的Solana交易
- ✅ **精确提取**mint地址和对应的真实市场地址
- ✅ **区分不同DEX**：Pump.fun vs Meteora
- ✅ **建立准确映射**：mint-to-market关系

## 🔍 核心发现

### MEV交易模式
- **MEV合约**: `MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz`
- **交易结构**: MEV合约执行一个大指令，包含所有相关账户

### 精确提取规律
1. **Pump.fun市场提取**:
   - 找到程序ID: `pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA`
   - 市场地址 = 程序位置 + 4

2. **Meteora市场提取**:
   - 找到程序ID: `LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo`
   - 市场地址 = 程序位置 + 2

## 🚀 使用方法

### 1. 简单分析单个交易

```javascript
const { SimpleMevMonitor } = require('./simple-mev-monitor.js');

const monitor = new SimpleMevMonitor('https://api.mainnet-beta.solana.com');

// 分析特定交易
const signature = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';
const mapping = await monitor.analyzeTransaction(signature);

console.log(mapping);
```

### 2. 实时监听MEV交易

```javascript
const { LiveMevMonitor } = require('./live-mev-monitor.js');

const monitor = new LiveMevMonitor('https://api.mainnet-beta.solana.com');

// 设置回调
monitor.onMevTransaction((signature, mapping) => {
  console.log(`检测到MEV交易: ${signature}`);
  
  Object.entries(mapping).forEach(([mint, info]) => {
    console.log(`Mint ${info.symbol}:`);
    info.markets.pump.forEach(market => {
      console.log(`  Pump.fun: ${market.address}`);
    });
    info.markets.meteora.forEach(market => {
      console.log(`  Meteora: ${market.address}`);
    });
  });
});

// 开始监听
await monitor.startLiveMonitoring();
```

### 3. 运行实时监听器

```bash
# 测试单个交易
node simple-mev-monitor.js

# 启动实时监听
node live-mev-monitor.js
```

## 📊 输出格式

### Mint-to-Market映射结构

```javascript
{
  "********************************************": {
    "mint": "********************************************",
    "symbol": "39zSVsSH",
    "decimals": 6,
    "markets": {
      "pump": [
        {
          "address": "BdjfXqH2MDPhuoaMUgKPy4RwF6sStUb8nDqjrJGgDUaC",
          "method": "MEV_PATTERN_PUMP_+4",
          "programPosition": 11,
          "marketPosition": 15
        }
      ],
      "meteora": [
        {
          "address": "22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH",
          "method": "MEV_PATTERN_METEORA_+2",
          "programPosition": 51,
          "marketPosition": 53
        }
      ]
    }
  }
}
```

## 🎯 验证结果

### 已验证的市场地址
- ✅ **Meteora市场**: `22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH`
- ✅ **Pump.fun市场**: `BdjfXqH2MDPhuoaMUgKPy4RwF6sStUb8nDqjrJGgDUaC`

### 测试交易
- **交易签名**: `NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj`
- **检测到**: 2个mint，6个市场（2个Pump.fun + 4个Meteora）

## 🔧 配置选项

### RPC端点
```javascript
// 主网
const RPC_URL = 'https://api.mainnet-beta.solana.com';

// 或使用其他RPC提供商
const RPC_URL = 'https://your-rpc-provider.com';
```

### 监听参数
```javascript
// 确认级别
'confirmed'  // 推荐：平衡速度和可靠性
'finalized'  // 最安全但较慢
'processed'  // 最快但可能有回滚
```

## 📈 性能优化

### 1. 交易去重
- 自动避免重复处理同一交易
- 维护最近1000个交易的缓存

### 2. 错误处理
- 网络错误自动重试
- 解析失败优雅降级
- 完整的错误回调机制

### 3. 内存管理
- 定期清理旧交易记录
- 限制缓存大小

## 🚨 注意事项

### 1. RPC限制
- 免费RPC有请求限制
- 建议使用付费RPC服务
- 考虑实现请求限流

### 2. 网络延迟
- 实时监听可能有1-3秒延迟
- 高频交易需要考虑时序

### 3. 数据准确性
- 基于已验证的模式提取
- 建议定期验证新的交易模式

## 🔮 扩展功能

### 1. 数据库存储
```javascript
// 可以添加数据库存储
monitor.onMevTransaction(async (signature, mapping) => {
  await database.saveMevTransaction(signature, mapping);
});
```

### 2. 通知系统
```javascript
// 可以添加通知功能
monitor.onMevTransaction(async (signature, mapping) => {
  await sendNotification(`检测到MEV交易: ${signature}`);
});
```

### 3. 统计分析
```javascript
// 可以添加统计功能
const stats = {
  totalTransactions: 0,
  uniqueMints: new Set(),
  marketCounts: { pump: 0, meteora: 0 }
};
```

## 📞 支持

如果遇到问题或需要扩展功能，请检查：
1. RPC连接是否正常
2. 交易是否真的包含MEV合约
3. 网络延迟是否影响实时性

## 🎉 成功案例

已成功提取的真实市场地址：
- `22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH` (Meteora TAP-WSOL Market)
- `BdjfXqH2MDPhuoaMUgKPy4RwF6sStUb8nDqjrJGgDUaC` (Pump.fun AP-WSOL Market)
- `JDfEHyw1sGWNrRGvjPE5uFKatPkyVViRcaveVp1FDcDc` (Meteora Market)
- `Cg4T8JXfSXVC1sAYzKdntu9kUnd5i3CVxAgoyBK7hXyH` (Pump.fun Market)

这些地址都是从真实的MEV交易中精确提取的！
