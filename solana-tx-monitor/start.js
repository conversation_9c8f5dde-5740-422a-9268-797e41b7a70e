#!/usr/bin/env node

/**
 * MEV监听器启动脚本
 *
 * 使用方法:
 * node start.js [地址]                    # 启动实时监听指定地址
 * node start.js test                      # 测试单个交易
 * node start.js test [signature]          # 测试指定交易
 * node start.js [地址] --live             # 启动实时监听（明确指定）
 */

require('dotenv').config();
const { LiveMevMonitor } = require('./live-mev-monitor.js');

// 从环境变量获取配置
const RPC_URL = process.env.RPC_URL ;
const TEST_TRANSACTION = process.env.TEST_TRANSACTION || 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';
const MEV_CONTRACT = process.env.MEV_CONTRACT || 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz';
const DEFAULT_TARGET_ADDRESS = process.env.TARGET_ADDRESS || null;

// 配置选项
const CONFIG = {
  maxMints: parseInt(process.env.MAX_MINTS) || 10,           // 最多保留的mint数量
  rpcUrl: RPC_URL,                                           // RPC端点
  confirmationLevel: process.env.CONFIRMATION_LEVEL || 'confirmed',  // 确认级别
  mevContract: MEV_CONTRACT,                                 // MEV合约地址
  defaultTargetAddress: DEFAULT_TARGET_ADDRESS              // 默认监听地址
};

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  console.log('🎯 MEV交易监听器');
  console.log('================');
  console.log(`⚙️  配置: 最多保留 ${CONFIG.maxMints} 个mint`);
  console.log(`🌐 RPC: ${CONFIG.rpcUrl}`);
  console.log('');

  // 解析命令行参数
  let targetAddress = null;
  let isLiveMode = false;
  let testSignature = null;

  if (command === 'test') {
    // 测试模式
    testSignature = args[1] || TEST_TRANSACTION;
  } else if (command && command !== '--help' && command !== '-h') {
    // 第一个参数是地址
    targetAddress = command;
    isLiveMode = args.includes('--live') || args.length === 1;
  } else if (!command && CONFIG.defaultTargetAddress) {
    // 使用环境变量中的默认地址
    targetAddress = CONFIG.defaultTargetAddress;
    isLiveMode = true;
  }

  try {
    const monitor = new LiveMevMonitor(CONFIG.rpcUrl, {
      maxMints: CONFIG.maxMints,
      targetAddress: targetAddress
    });

    // 设置回调
    monitor.onMevTransaction((signature, mapping) => {
      console.log('\n🎉 MEV交易处理完成!');
      console.log(`   交易: ${signature.slice(0, 8)}...${signature.slice(-8)}`);
      console.log(`   检测到 ${Object.keys(mapping).length} 个mint-market组`);
      
      Object.entries(mapping).forEach(([mintAddr, info], index) => {
        const totalMarkets = info.markets.pump.length + info.markets.meteora.length;
        console.log(`   组${index + 1} ${info.symbol}: ${totalMarkets}个市场 (${info.markets.pump.length}个Pump + ${info.markets.meteora.length}个Meteora)`);
      });
    });

    monitor.onError((error) => {
      console.error('❌ 监听器错误:', error.message);
    });

    if (command === 'test') {
      // 测试模式
      console.log('🧪 测试模式');
      console.log(`📝 测试交易: ${testSignature}`);
      console.log('');

      const result = await monitor.analyzeTransaction(testSignature);
      
      if (result) {
        console.log('\n✅ 测试成功!');
        
        // 显示数据库状态
        const dbStatus = monitor.getMintDatabase();
        console.log('\n📊 Mint数据库状态:');
        dbStatus.mints.forEach((mint, index) => {
          console.log(`   ${index + 1}. ${mint.symbol} (${mint.mint.slice(0, 8)}...)`);
          console.log(`      首次发现: ${mint.firstSeen.toLocaleString()}`);
          console.log(`      更新次数: ${mint.updateCount}`);
          console.log(`      市场数量: ${mint.pumpMarkets + mint.meteoraMarkets}个`);
        });
        
      } else {
        console.log('❌ 测试失败');
        process.exit(1);
      }

    } else if (isLiveMode && targetAddress) {
      // 实时监听模式
      console.log('🚀 启动实时监听模式');
      console.log(`🎯 监听地址: ${targetAddress}`);
      console.log(`🔍 检查MEV合约: ${CONFIG.mevContract}`);
      console.log('⚠️  按 Ctrl+C 停止监听');
      console.log('');

      await monitor.startLiveMonitoring();

      // 设置优雅退出
      process.on('SIGINT', async () => {
        console.log('\n\n🛑 正在停止监听器...');
        await monitor.stopLiveMonitoring();
        
        const stats = monitor.getStats();
        const dbStatus = monitor.getMintDatabase();
        
        console.log('\n📊 最终统计:');
        console.log(`   处理交易: ${stats.processedCount}个`);
        console.log(`   Mint数据: ${dbStatus.size}个`);
        
        if (dbStatus.size > 0) {
          console.log('\n💾 保存的Mint数据:');
          dbStatus.mints.forEach((mint, index) => {
            console.log(`   ${index + 1}. ${mint.symbol}: ${mint.pumpMarkets + mint.meteoraMarkets}个市场`);
          });
        }
        
        console.log('\n👋 监听器已停止');
        process.exit(0);
      });

      // 定期显示状态
      setInterval(() => {
        const stats = monitor.getStats();
        const dbStatus = monitor.getMintDatabase();
        if (stats.isMonitoring) {
          console.log(`📊 ${new Date().toLocaleString()} | 处理: ${stats.processedCount}个交易 | Mint: ${dbStatus.size}/${dbStatus.maxSize}`);
        }
      }, 30000); // 每30秒显示一次
    } else {
      // 没有提供有效参数
      console.log('❌ 请提供要监听的地址或使用测试模式');
      console.log('');
      showUsage();
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ 程序错误:', error.message);
    process.exit(1);
  }
}

// 显示使用说明
function showUsage() {
  console.log('使用方法:');
  console.log('  node start.js [地址]                    # 监听指定地址的MEV交易');
  console.log('  node start.js test                      # 测试已知交易');
  console.log('  node start.js test [signature]          # 测试指定交易');
  console.log('');
  console.log('示例:');
  console.log('  node start.js 7dJjhC7ehSF9XKHyGZJGwXiFPm6Ss5NPrG7NZpgWt4n3');
  console.log('  node start.js test');
  console.log('');
  console.log('说明:');
  console.log('  - 监听指定地址的所有交易');
  console.log('  - 只处理包含MEV合约的交易');
  console.log(`  - MEV合约: ${CONFIG.mevContract}`);
  console.log('');
  console.log('配置:');
  console.log(`  最大mint数量: ${CONFIG.maxMints}`);
  console.log(`  RPC端点: ${CONFIG.rpcUrl}`);
  console.log(`  确认级别: ${CONFIG.confirmationLevel}`);
  if (CONFIG.defaultTargetAddress) {
    console.log(`  默认监听地址: ${CONFIG.defaultTargetAddress}`);
  }
  console.log('');
}

// 检查参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  showUsage();
  process.exit(0);
}

// 运行主程序
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 启动失败:', error.message);
    process.exit(1);
  });
}
