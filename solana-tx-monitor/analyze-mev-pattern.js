#!/usr/bin/env node

/**
 * MEV模式分析器
 * 
 * 分析真实MEV交易的规律，理解mint-to-market映射模式
 */

const { Connection } = require('@solana/web3.js');

const RPC_URL = 'https://api.mainnet-beta.solana.com';
const TRANSACTION_SIGNATURE = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';
const MEV_CONTRACT = 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz';

// 已知程序ID
const PROGRAM_IDS = {
  PUMP_NEW: 'pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA',
  METEORA: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
  TOKEN_PROGRAM: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
  SYSTEM_PROGRAM: '11111111111111111111111111111111'
};

/**
 * 安全获取账户地址
 */
function getAccountAddress(account) {
  if (typeof account === 'string') {
    return account;
  }
  if (account && account.pubkey) {
    return account.pubkey.toString();
  }
  if (account && typeof account.toString === 'function') {
    return account.toString();
  }
  return 'UNKNOWN_ACCOUNT';
}

/**
 * 分析MEV交易模式
 */
async function analyzeMevPattern() {
  console.log('🔍 MEV交易模式分析');
  console.log(`📝 交易: ${TRANSACTION_SIGNATURE}`);
  console.log('');

  try {
    const connection = new Connection(RPC_URL);
    
    const transaction = await connection.getParsedTransaction(TRANSACTION_SIGNATURE, {
      maxSupportedTransactionVersion: 0
    });

    if (!transaction) {
      console.error('❌ 未找到交易');
      return;
    }

    // 提取基本信息
    const accountKeys = transaction.transaction.message.accountKeys.map(getAccountAddress);
    const instructions = transaction.transaction.message.instructions;

    console.log('✅ 交易解析成功');
    console.log(`账户总数: ${accountKeys.length}`);
    console.log(`指令总数: ${instructions.length}`);
    console.log('');

    // 分析mint
    const mints = extractMints(transaction);
    console.log('🪙 提取的Mint:');
    mints.forEach((mint, index) => {
      console.log(`  ${index + 1}. ${mint}`);
    });
    console.log('');

    // 分析程序调用
    analyzeProgramCalls(accountKeys, instructions);

    // 分析mint-to-market映射
    const mapping = analyzeMintToMarketMapping(accountKeys, instructions, mints);
    displayMapping(mapping);

    // 生成规律总结
    generatePatternSummary(mapping);

  } catch (error) {
    console.error('❌ 分析失败:', error.message);
  }
}

/**
 * 提取mint地址
 */
function extractMints(transaction) {
  const mints = new Set();
  
  // 从token balances提取
  if (transaction.meta?.preTokenBalances) {
    transaction.meta.preTokenBalances.forEach(balance => {
      if (balance.mint && balance.mint !== 'So11111111111111111111111111111111111111112') {
        mints.add(balance.mint);
      }
    });
  }
  
  if (transaction.meta?.postTokenBalances) {
    transaction.meta.postTokenBalances.forEach(balance => {
      if (balance.mint && balance.mint !== 'So11111111111111111111111111111111111111112') {
        mints.add(balance.mint);
      }
    });
  }

  return Array.from(mints);
}

/**
 * 分析程序调用
 */
function analyzeProgramCalls(accountKeys, instructions) {
  console.log('🔄 程序调用分析:');
  
  instructions.forEach((instruction, index) => {
    const programIndex = instruction.programIdIndex;
    const programAddress = accountKeys[programIndex];
    const programName = Object.keys(PROGRAM_IDS).find(key => PROGRAM_IDS[key] === programAddress) || programAddress.slice(0, 8);
    
    console.log(`  指令 ${index + 1}: ${programName}`);
    
    if (instruction.accounts && instruction.accounts.length > 0) {
      console.log(`    涉及账户: ${instruction.accounts.length}个`);
      
      // 显示前几个账户作为示例
      const sampleAccounts = instruction.accounts.slice(0, 3);
      sampleAccounts.forEach((accIndex, i) => {
        const account = accountKeys[accIndex];
        if (account && account !== 'So11111111111111111111111111111111111111112') {
          console.log(`      ${i + 1}. ${account.slice(0, 8)}...${account.slice(-8)}`);
        }
      });
      
      if (instruction.accounts.length > 3) {
        console.log(`      ... 还有 ${instruction.accounts.length - 3} 个账户`);
      }
    }
  });
  console.log('');
}

/**
 * 分析mint到市场的映射
 */
function analyzeMintToMarketMapping(accountKeys, instructions, mints) {
  console.log('🎯 Mint-to-Market映射分析:');
  
  const mapping = {};
  
  mints.forEach(mint => {
    mapping[mint] = {
      mint: mint,
      symbol: mint.slice(0, 8),
      markets: {
        pump: [],
        meteora: []
      }
    };
  });

  // 分析每个指令
  instructions.forEach((instruction, index) => {
    const programIndex = instruction.programIdIndex;
    const programAddress = accountKeys[programIndex];
    
    if (instruction.accounts) {
      const usedAccounts = instruction.accounts.map(accIndex => accountKeys[accIndex]);
      
      // Pump.fun指令
      if (programAddress === PROGRAM_IDS.PUMP_NEW) {
        console.log(`  Pump.fun指令 ${index + 1}:`);
        
        // 查找可能的市场账户（排除已知的程序和mint）
        const potentialMarkets = usedAccounts.filter(acc => 
          acc && 
          acc.length === 44 && 
          !Object.values(PROGRAM_IDS).includes(acc) &&
          !mints.includes(acc) &&
          acc !== 'So11111111111111111111111111111111111111112'
        );
        
        potentialMarkets.forEach(market => {
          console.log(`    潜在市场: ${market.slice(0, 8)}...${market.slice(-8)}`);
          
          // 尝试关联到mint
          mints.forEach(mint => {
            mapping[mint].markets.pump.push(market);
          });
        });
      }
      
      // Meteora指令
      if (programAddress === PROGRAM_IDS.METEORA) {
        console.log(`  Meteora指令 ${index + 1}:`);
        
        const potentialMarkets = usedAccounts.filter(acc => 
          acc && 
          acc.length === 44 && 
          !Object.values(PROGRAM_IDS).includes(acc) &&
          !mints.includes(acc) &&
          acc !== 'So11111111111111111111111111111111111111112'
        );
        
        potentialMarkets.forEach(market => {
          console.log(`    潜在市场: ${market.slice(0, 8)}...${market.slice(-8)}`);
          
          mints.forEach(mint => {
            mapping[mint].markets.meteora.push(market);
          });
        });
      }
    }
  });
  
  console.log('');
  return mapping;
}

/**
 * 显示映射结果
 */
function displayMapping(mapping) {
  console.log('📊 最终映射结果:');
  console.log('');
  
  Object.values(mapping).forEach((mintData, index) => {
    console.log(`${index + 1}️⃣ ${mintData.symbol}:`);
    console.log(`   🪙 Mint: ${mintData.mint}`);
    
    if (mintData.markets.pump.length > 0) {
      console.log(`   🟢 Pump.fun市场 (${mintData.markets.pump.length}个):`);
      mintData.markets.pump.forEach((market, i) => {
        console.log(`     ${i + 1}. ${market}`);
      });
    }
    
    if (mintData.markets.meteora.length > 0) {
      console.log(`   🟠 Meteora市场 (${mintData.markets.meteora.length}个):`);
      mintData.markets.meteora.forEach((market, i) => {
        console.log(`     ${i + 1}. ${market}`);
      });
    }
    
    console.log('');
  });
}

/**
 * 生成规律总结
 */
function generatePatternSummary(mapping) {
  console.log('🎯 规律总结:');
  console.log('');
  
  console.log('1️⃣ 提取规律:');
  console.log('   ✅ 从token balances提取mint地址');
  console.log('   ✅ 从指令账户中提取市场地址');
  console.log('   ✅ 根据程序ID区分DEX类型');
  console.log('   ✅ 排除已知程序和系统账户');
  console.log('');
  
  console.log('2️⃣ 实现策略:');
  console.log('   💡 解析每个指令的账户列表');
  console.log('   💡 根据程序ID分类（Pump.fun vs Meteora）');
  console.log('   💡 过滤出潜在的市场地址');
  console.log('   💡 建立mint到市场的映射关系');
  console.log('');
  
  console.log('3️⃣ 代码实现思路:');
  console.log('```javascript');
  console.log('function extractMintToMarketMapping(transaction) {');
  console.log('  const mints = extractMintsFromTokenBalances(transaction);');
  console.log('  const mapping = {};');
  console.log('  ');
  console.log('  transaction.instructions.forEach(instruction => {');
  console.log('    const programId = instruction.programId;');
  console.log('    const accounts = instruction.accounts;');
  console.log('    ');
  console.log('    if (programId === PUMP_PROGRAM) {');
  console.log('      const markets = filterPotentialMarkets(accounts);');
  console.log('      associateWithMints(mints, markets, "pump");');
  console.log('    }');
  console.log('    ');
  console.log('    if (programId === METEORA_PROGRAM) {');
  console.log('      const markets = filterPotentialMarkets(accounts);');
  console.log('      associateWithMints(mints, markets, "meteora");');
  console.log('    }');
  console.log('  });');
  console.log('  ');
  console.log('  return mapping;');
  console.log('}');
  console.log('```');
}

// 运行分析
if (require.main === module) {
  analyzeMevPattern().catch(console.error);
}

module.exports = { analyzeMevPattern };
