#!/usr/bin/env node

/**
 * 检查交易结构
 */

const { Connection } = require('@solana/web3.js');

const RPC_URL = 'https://api.mainnet-beta.solana.com';
const TEST_TRANSACTION = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';

async function inspectTransaction() {
  console.log('🔍 检查交易结构');
  console.log(`📝 测试交易: ${TEST_TRANSACTION}`);
  console.log('');

  try {
    const connection = new Connection(RPC_URL);
    
    // 获取交易
    const transaction = await connection.getParsedTransaction(TEST_TRANSACTION, {
      maxSupportedTransactionVersion: 0
    });

    if (!transaction) {
      console.error('❌ 交易未找到');
      return;
    }

    console.log('✅ 交易获取成功');
    console.log('');

    // 检查整体结构
    console.log('📊 交易整体结构:');
    console.log(`   transaction: ${!!transaction.transaction}`);
    console.log(`   meta: ${!!transaction.meta}`);
    console.log(`   slot: ${transaction.slot}`);
    console.log(`   blockTime: ${transaction.blockTime}`);
    console.log('');

    // 检查message结构
    if (transaction.transaction && transaction.transaction.message) {
      const message = transaction.transaction.message;
      console.log('📝 Message结构:');
      console.log(`   accountKeys: ${!!message.accountKeys} (${message.accountKeys?.length}个)`);
      console.log(`   instructions: ${!!message.instructions} (${message.instructions?.length}个)`);
      console.log(`   recentBlockhash: ${!!message.recentBlockhash}`);
      console.log('');

      // 检查指令结构
      if (message.instructions) {
        console.log('🔄 指令详细结构:');
        message.instructions.forEach((instruction, index) => {
          console.log(`   指令 ${index + 1}:`);
          console.log(`     类型: ${typeof instruction}`);
          console.log(`     键: [${Object.keys(instruction).join(', ')}]`);
          
          if (instruction.programIdIndex !== undefined) {
            console.log(`     programIdIndex: ${instruction.programIdIndex}`);
          }
          
          if (instruction.accounts !== undefined) {
            console.log(`     accounts: ${Array.isArray(instruction.accounts) ? instruction.accounts.length : 'not array'}`);
          }
          
          if (instruction.data !== undefined) {
            console.log(`     data: ${typeof instruction.data}`);
          }
          
          // 显示完整的指令对象
          console.log(`     完整对象:`, JSON.stringify(instruction, null, 2));
          console.log('');
        });
      }

      // 检查账户密钥结构
      if (message.accountKeys) {
        console.log('🔑 账户密钥结构:');
        console.log(`   总数: ${message.accountKeys.length}`);
        
        // 显示前几个账户的结构
        message.accountKeys.slice(0, 5).forEach((key, index) => {
          console.log(`   账户 ${index}:`);
          console.log(`     类型: ${typeof key}`);
          if (typeof key === 'object') {
            console.log(`     键: [${Object.keys(key).join(', ')}]`);
            if (key.pubkey) {
              console.log(`     pubkey: ${key.pubkey.toString()}`);
            }
          } else {
            console.log(`     值: ${key}`);
          }
        });
        console.log('');
      }
    }

    // 检查meta结构
    if (transaction.meta) {
      const meta = transaction.meta;
      console.log('📊 Meta结构:');
      console.log(`   logMessages: ${!!meta.logMessages} (${meta.logMessages?.length}个)`);
      console.log(`   preTokenBalances: ${!!meta.preTokenBalances} (${meta.preTokenBalances?.length}个)`);
      console.log(`   postTokenBalances: ${!!meta.postTokenBalances} (${meta.postTokenBalances?.length}个)`);
      console.log(`   err: ${meta.err}`);
      console.log('');

      // 显示token balances
      if (meta.preTokenBalances && meta.preTokenBalances.length > 0) {
        console.log('🪙 Token Balances示例:');
        meta.preTokenBalances.slice(0, 2).forEach((balance, index) => {
          console.log(`   Balance ${index}:`);
          console.log(`     mint: ${balance.mint}`);
          console.log(`     owner: ${balance.owner}`);
          console.log(`     decimals: ${balance.uiTokenAmount?.decimals}`);
        });
        console.log('');
      }
    }

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    console.error(error.stack);
  }
}

// 运行检查
if (require.main === module) {
  inspectTransaction().catch(console.error);
}

module.exports = { inspectTransaction };
