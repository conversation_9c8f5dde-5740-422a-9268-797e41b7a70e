#!/usr/bin/env node

/**
 * 实时MEV监听器
 * 
 * 监听包含MEV合约的实时交易并提取mint-to-market映射
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const { SimpleMevMonitor } = require('./simple-mev-monitor.js');

const RPC_URL = 'https://api.mainnet-beta.solana.com';
const MEV_CONTRACT = 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz';

class LiveMevMonitor extends SimpleMevMonitor {
  constructor(rpcUrl) {
    super(rpcUrl);
    this.subscriptionId = null;
    this.processedTransactions = new Set();
  }

  /**
   * 开始实时监听MEV交易
   */
  async startLiveMonitoring() {
    if (this.isMonitoring) {
      console.log('⚠️  监听器已在运行');
      return;
    }

    console.log('🚀 启动实时MEV监听器');
    console.log(`🎯 监听合约: ${MEV_CONTRACT}`);
    console.log('');

    try {
      const mevPubkey = new PublicKey(MEV_CONTRACT);
      
      // 订阅MEV合约的日志
      this.subscriptionId = this.connection.onLogs(
        mevPubkey,
        (logs, context) => {
          this.handleLiveTransaction(logs.signature, context.slot);
        },
        'confirmed'
      );

      this.isMonitoring = true;
      console.log('✅ 实时监听已启动');
      console.log('🔍 等待MEV交易...');
      console.log('');

    } catch (error) {
      console.error('❌ 启动监听失败:', error.message);
      if (this.onErrorCallback) {
        this.onErrorCallback(error);
      }
    }
  }

  /**
   * 停止实时监听
   */
  async stopLiveMonitoring() {
    if (!this.isMonitoring) {
      console.log('⚠️  监听器未在运行');
      return;
    }

    if (this.subscriptionId) {
      await this.connection.removeOnLogsListener(this.subscriptionId);
      this.subscriptionId = null;
    }

    this.isMonitoring = false;
    console.log('🛑 实时监听已停止');
  }

  /**
   * 处理实时交易
   */
  async handleLiveTransaction(signature, slot) {
    // 避免重复处理
    if (this.processedTransactions.has(signature)) {
      return;
    }
    this.processedTransactions.add(signature);

    // 清理旧的交易记录（保留最近1000个）
    if (this.processedTransactions.size > 1000) {
      const signatures = Array.from(this.processedTransactions);
      this.processedTransactions.clear();
      signatures.slice(-500).forEach(sig => this.processedTransactions.add(sig));
    }

    console.log(`\n🎯 检测到实时MEV交易!`);
    console.log(`   签名: ${signature}`);
    console.log(`   插槽: ${slot}`);
    console.log(`   时间: ${new Date().toLocaleString()}`);

    try {
      // 分析交易
      const mapping = await this.analyzeTransaction(signature);
      
      if (mapping) {
        console.log('✅ 实时分析成功!');
        
        // 统计信息
        const mintCount = Object.keys(mapping).length;
        let totalMarkets = 0;
        
        Object.values(mapping).forEach(info => {
          totalMarkets += info.markets.pump.length + info.markets.meteora.length;
        });
        
        console.log(`📊 实时统计: ${mintCount}个mint, ${totalMarkets}个市场`);
        
      } else {
        console.log('⚠️  实时分析失败或非MEV交易');
      }

    } catch (error) {
      console.error('❌ 处理实时交易失败:', error.message);
      if (this.onErrorCallback) {
        this.onErrorCallback(error);
      }
    }
  }

  /**
   * 获取监听统计
   */
  getStats() {
    return {
      isMonitoring: this.isMonitoring,
      processedCount: this.processedTransactions.size,
      subscriptionId: this.subscriptionId
    };
  }
}

// 主函数
async function main() {
  console.log('🎯 实时MEV监听器演示');
  console.log('');

  const monitor = new LiveMevMonitor(RPC_URL);
  
  // 设置回调
  monitor.onMevTransaction((signature, mapping) => {
    console.log('\n🎉 MEV交易处理完成!');
    console.log(`   交易: ${signature.slice(0, 8)}...${signature.slice(-8)}`);
    
    Object.entries(mapping).forEach(([mint, info]) => {
      const totalMarkets = info.markets.pump.length + info.markets.meteora.length;
      console.log(`   ${info.symbol}: ${totalMarkets}个市场 (${info.markets.pump.length}个Pump.fun + ${info.markets.meteora.length}个Meteora)`);
    });
  });

  monitor.onError((error) => {
    console.error('❌ 监听器错误:', error.message);
  });

  // 首先测试已知交易
  console.log('🧪 测试阶段: 分析已知MEV交易');
  const testTx = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';
  const testResult = await monitor.analyzeTransaction(testTx);
  
  if (testResult) {
    console.log('✅ 测试成功，监听器工作正常');
    console.log('');
    
    // 询问是否开始实时监听
    console.log('🚀 准备开始实时监听...');
    console.log('⚠️  注意: 实时监听会持续运行，按 Ctrl+C 停止');
    console.log('');
    
    // 开始实时监听
    await monitor.startLiveMonitoring();
    
    // 设置优雅退出
    process.on('SIGINT', async () => {
      console.log('\n\n🛑 收到退出信号，正在停止监听器...');
      await monitor.stopLiveMonitoring();
      
      const stats = monitor.getStats();
      console.log(`📊 监听统计: 处理了 ${stats.processedCount} 个交易`);
      console.log('👋 再见!');
      process.exit(0);
    });
    
    // 定期显示统计信息
    setInterval(() => {
      const stats = monitor.getStats();
      if (stats.isMonitoring) {
        console.log(`📊 监听状态: 活跃中 | 已处理: ${stats.processedCount}个交易 | 时间: ${new Date().toLocaleString()}`);
      }
    }, 60000); // 每分钟显示一次
    
  } else {
    console.log('❌ 测试失败，请检查配置');
    process.exit(1);
  }
}

// 导出类
module.exports = { LiveMevMonitor };

// 如果直接运行
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 程序错误:', error.message);
    process.exit(1);
  });
}
