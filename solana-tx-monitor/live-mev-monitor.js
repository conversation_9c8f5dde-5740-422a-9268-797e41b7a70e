#!/usr/bin/env node

/**
 * 实时MEV监听器
 *
 * 监听包含MEV合约的实时交易并提取mint-to-market映射
 */

require('dotenv').config();
const { Connection, PublicKey } = require('@solana/web3.js');
const { SimpleMevMonitor } = require('./simple-mev-monitor.js');

// 从环境变量获取配置
const RPC_URL = process.env.RPC_URL ;
const MEV_CONTRACT = process.env.MEV_CONTRACT || 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz';
const DEFAULT_TARGET_ADDRESS = process.env.TARGET_ADDRESS || null;

class LiveMevMonitor extends SimpleMevMonitor {
  constructor(rpcUrl, options = {}) {
    super(rpcUrl, options);
    this.subscriptionId = null;
    this.processedTransactions = new Set();
    this.targetAddress = options.targetAddress || null; // 要监听的特定地址
  }

  /**
   * 开始实时监听特定地址的交易
   */
  async startLiveMonitoring() {
    if (this.isMonitoring) {
      console.log('⚠️  监听器已在运行');
      return;
    }

    if (!this.targetAddress) {
      console.error('❌ 未设置目标监听地址');
      return;
    }

    console.log('🚀 启动实时地址监听器');
    console.log(`🎯 监听地址: ${this.targetAddress}`);
    console.log(`🔍 检查是否包含MEV合约: ${MEV_CONTRACT}`);
    console.log('');

    try {
      const targetPubkey = new PublicKey(this.targetAddress);

      // 订阅目标地址的日志
      this.subscriptionId = this.connection.onLogs(
        targetPubkey,
        (logs, context) => {
          this.handleLiveTransaction(logs.signature, context.slot);
        },
        'confirmed'
      );

      this.isMonitoring = true;
      console.log('✅ 实时监听已启动');
      console.log('🔍 等待包含MEV合约的交易...');
      console.log('');

    } catch (error) {
      console.error('❌ 启动监听失败:', error.message);
      if (this.onErrorCallback) {
        this.onErrorCallback(error);
      }
    }
  }

  /**
   * 停止实时监听
   */
  async stopLiveMonitoring() {
    if (!this.isMonitoring) {
      console.log('⚠️  监听器未在运行');
      return;
    }

    if (this.subscriptionId) {
      await this.connection.removeOnLogsListener(this.subscriptionId);
      this.subscriptionId = null;
    }

    this.isMonitoring = false;
    console.log('🛑 实时监听已停止');
  }

  /**
   * 处理实时交易
   */
  async handleLiveTransaction(signature, slot) {
    // 避免重复处理
    if (this.processedTransactions.has(signature)) {
      return;
    }
    this.processedTransactions.add(signature);

    // 清理旧的交易记录（保留最近1000个）
    if (this.processedTransactions.size > 1000) {
      const signatures = Array.from(this.processedTransactions);
      this.processedTransactions.clear();
      signatures.slice(-500).forEach(sig => this.processedTransactions.add(sig));
    }

    console.log(`\n📡 检测到地址交易: ${signature.slice(0, 8)}...${signature.slice(-8)}`);
    console.log(`   插槽: ${slot}`);
    console.log(`   时间: ${new Date().toLocaleString()}`);

    try {
      // 首先检查交易是否包含MEV合约
      const containsMev = await this.checkTransactionForMevContract(signature);

      if (!containsMev) {
        console.log('   ⚪ 交易不包含MEV合约，跳过');
        return;
      }

      console.log('   🎯 发现包含MEV合约的交易！');

      // 分析交易
      const mapping = await this.analyzeTransaction(signature);

      if (mapping) {
        console.log('   ✅ MEV交易分析成功!');

        // 统计信息
        const mintCount = Object.keys(mapping).length;
        let totalMarkets = 0;

        Object.values(mapping).forEach(info => {
          totalMarkets += info.markets.pump.length + info.markets.meteora.length;
        });

        console.log(`   📊 统计: ${mintCount}个mint, ${totalMarkets}个市场`);

      } else {
        console.log('   ⚠️  MEV交易分析失败');
      }

    } catch (error) {
      console.error('❌ 处理实时交易失败:', error.message);
      if (this.onErrorCallback) {
        this.onErrorCallback(error);
      }
    }
  }

  /**
   * 检查交易是否包含MEV合约
   */
  async checkTransactionForMevContract(signature) {
    try {
      const transaction = await this.connection.getParsedTransaction(signature, {
        maxSupportedTransactionVersion: 0
      });

      if (!transaction) {
        return false;
      }

      // 检查指令中是否包含MEV合约
      const instructions = transaction.transaction.message.instructions;

      for (const instruction of instructions) {
        const programIdStr = instruction.programId ? instruction.programId.toString() : '';
        if (programIdStr === MEV_CONTRACT) {
          return true;
        }
      }

      return false;

    } catch (error) {
      console.warn(`检查MEV合约失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取监听统计
   */
  getStats() {
    return {
      isMonitoring: this.isMonitoring,
      processedCount: this.processedTransactions.size,
      subscriptionId: this.subscriptionId
    };
  }
}

// 主函数
async function main() {
  console.log('🎯 实时地址监听器');
  console.log('');

  // 配置选项
  const options = {
    maxMints: 10, // 最多保留10个mint
    targetAddress: TARGET_ADDRESS // 要监听的特定地址
  };

  // 检查是否设置了目标地址
  if (!options.targetAddress || options.targetAddress === 'YOUR_TARGET_ADDRESS_HERE') {
    console.error('❌ 请先设置要监听的目标地址！');
    console.log('');
    console.log('修改方法:');
    console.log('1. 编辑 live-mev-monitor.js 文件');
    console.log('2. 将 TARGET_ADDRESS 设置为你要监听的地址');
    console.log('3. 例如: const TARGET_ADDRESS = "你的地址";');
    process.exit(1);
  }

  const monitor = new LiveMevMonitor(RPC_URL, options);

  console.log(`⚙️  配置:`);
  console.log(`   监听地址: ${options.targetAddress}`);
  console.log(`   最多保留: ${options.maxMints} 个mint数据`);
  console.log(`   MEV合约: ${MEV_CONTRACT}`);
  console.log('');
  
  // 设置回调
  monitor.onMevTransaction((signature, mapping) => {
    console.log('\n🎉 MEV交易处理完成!');
    console.log(`   交易: ${signature.slice(0, 8)}...${signature.slice(-8)}`);
    console.log(`   检测到 ${Object.keys(mapping).length} 个独立的mint-market组`);

    Object.entries(mapping).forEach(([mint, info], index) => {
      const totalMarkets = info.markets.pump.length + info.markets.meteora.length;
      console.log(`   组${index + 1} ${info.symbol}: ${totalMarkets}个市场 (${info.markets.pump.length}个Pump.fun + ${info.markets.meteora.length}个Meteora)`);
    });
  });

  monitor.onError((error) => {
    console.error('❌ 监听器错误:', error.message);
  });

  // 首先测试已知交易
  console.log('🧪 测试阶段: 分析已知MEV交易');
  const testTx = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';
  const testResult = await monitor.analyzeTransaction(testTx);
  
  if (testResult) {
    console.log('✅ 测试成功，监听器工作正常');
    console.log('');
    
    // 询问是否开始实时监听
    console.log('🚀 准备开始实时监听...');
    console.log('⚠️  注意: 实时监听会持续运行，按 Ctrl+C 停止');
    console.log('');
    
    // 开始实时监听
    await monitor.startLiveMonitoring();
    
    // 设置优雅退出
    process.on('SIGINT', async () => {
      console.log('\n\n🛑 收到退出信号，正在停止监听器...');
      await monitor.stopLiveMonitoring();
      
      const stats = monitor.getStats();
      console.log(`📊 监听统计: 处理了 ${stats.processedCount} 个交易`);
      console.log('👋 再见!');
      process.exit(0);
    });
    
    // 定期显示统计信息
    setInterval(() => {
      const stats = monitor.getStats();
      const dbStatus = monitor.getMintDatabase();
      if (stats.isMonitoring) {
        console.log(`📊 监听状态: 活跃中 | 已处理: ${stats.processedCount}个交易 | Mint数据: ${dbStatus.size}/${dbStatus.maxSize} | 时间: ${new Date().toLocaleString()}`);
      }
    }, 60000); // 每分钟显示一次
    
  } else {
    console.log('❌ 测试失败，请检查配置');
    process.exit(1);
  }
}

// 导出类
module.exports = { LiveMevMonitor };

// 如果直接运行
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 程序错误:', error.message);
    process.exit(1);
  });
}
