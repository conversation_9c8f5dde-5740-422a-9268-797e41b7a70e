#!/usr/bin/env node

/**
 * MEV交易分析器
 * 
 * 专门分析包含MEV合约的交易，提取mint和对应的市场池信息
 * MEV合约地址: MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz
 */

const { Connection, PublicKey } = require('@solana/web3.js');

// 配置
const RPC_URL = 'https://api.mainnet-beta.solana.com';
const MEV_CONTRACT = 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz';
const TRANSACTION_SIGNATURE = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';

// 已知程序映射
const KNOWN_PROGRAMS = {
  '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P': 'Pump.fun',
  'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo': 'Meteora DLMM',
  'CAMMCzo5YL8w4VFF8KVHrK22GGUQpMkFr9WeqATV9Uu': 'Raydium CLMM',
  'whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc': 'Orca Whirlpool',
  'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA': 'Token Program',
  'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL': 'Associated Token Program',
  '11111111111111111111111111111111': 'System Program',
  'ComputeBudget111111111111111111111111111111': 'Compute Budget Program'
};

// 已知代币映射
const KNOWN_TOKENS = {
  'So11111111111111111111111111111111111111112': 'SOL',
  'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC',
  'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'USDT'
};

/**
 * 分析MEV交易
 */
async function analyzeMevTransaction() {
  console.log('🤖 MEV交易分析器启动');
  console.log(`📝 交易签名: ${TRANSACTION_SIGNATURE}`);
  console.log(`🔍 MEV合约: ${MEV_CONTRACT}`);
  console.log(`🔗 Solscan: https://solscan.io/tx/${TRANSACTION_SIGNATURE}`);
  console.log('');

  try {
    const connection = new Connection(RPC_URL);
    
    // 获取交易详情
    console.log('📡 正在获取交易详情...');
    const transaction = await connection.getParsedTransaction(TRANSACTION_SIGNATURE, {
      maxSupportedTransactionVersion: 0
    });

    if (!transaction) {
      console.error('❌ 未找到交易');
      return;
    }

    // 检查是否包含MEV合约
    const containsMevContract = checkMevContract(transaction);
    if (!containsMevContract) {
      console.log('⚠️  该交易不包含指定的MEV合约');
      return;
    }

    console.log('✅ 检测到MEV合约交易');
    console.log('');

    // 分析基本信息
    analyzeBasicInfo(transaction);

    // 分析mint地址
    const mints = analyzeMints(transaction);

    // 分析市场池信息
    await analyzeMarketPools(transaction, mints);

    // 分析交易流程
    analyzeTransactionFlow(transaction);

    console.log('✅ MEV交易分析完成');
    
  } catch (error) {
    console.error('❌ 分析失败:', error.message);
  }
}

/**
 * 检查交易是否包含MEV合约
 */
function checkMevContract(transaction) {
  // 检查日志消息
  if (transaction.meta?.logMessages) {
    for (const log of transaction.meta.logMessages) {
      if (log.includes(MEV_CONTRACT)) {
        return true;
      }
    }
  }

  // 检查账户密钥
  const accountKeys = transaction.transaction.message.accountKeys;
  for (const key of accountKeys) {
    const keyStr = typeof key === 'string' ? key : key.pubkey?.toString() || key.toString();
    if (keyStr === MEV_CONTRACT) {
      return true;
    }
  }

  return false;
}

/**
 * 分析基本信息
 */
function analyzeBasicInfo(transaction) {
  console.log('📊 基本信息:');
  console.log(`  插槽: ${transaction.slot}`);
  console.log(`  区块时间: ${transaction.blockTime ? new Date(transaction.blockTime * 1000).toLocaleString() : '未知'}`);
  console.log(`  手续费: ${transaction.meta?.fee || 0} lamports`);
  console.log(`  状态: ${transaction.meta?.err ? '失败' : '成功'}`);
  console.log('');
}

/**
 * 分析mint地址
 */
function analyzeMints(transaction) {
  const mints = new Set();
  
  // 从token balances获取
  if (transaction.meta?.preTokenBalances) {
    transaction.meta.preTokenBalances.forEach(balance => {
      if (balance.mint) mints.add(balance.mint);
    });
  }
  
  if (transaction.meta?.postTokenBalances) {
    transaction.meta.postTokenBalances.forEach(balance => {
      if (balance.mint) mints.add(balance.mint);
    });
  }

  if (mints.size > 0) {
    console.log('🪙 检测到的Mint地址:');
    Array.from(mints).forEach((mint, index) => {
      const symbol = KNOWN_TOKENS[mint] || mint.slice(0, 8);
      console.log(`  ${index + 1}. ${mint}`);
      console.log(`     代币: ${symbol}`);
    });
    console.log('');
  }

  return Array.from(mints);
}

/**
 * 分析市场池信息
 */
async function analyzeMarketPools(transaction, mints) {
  console.log('🏊 市场池分析:');
  
  // 从交易日志中提取市场池信息
  const marketPools = extractMarketPoolsFromLogs(transaction);
  
  if (marketPools.length > 0) {
    console.log('  从交易中检测到的市场池:');
    marketPools.forEach((pool, index) => {
      console.log(`    ${index + 1}. ${pool.type}: ${pool.address}`);
      if (pool.description) {
        console.log(`       ${pool.description}`);
      }
    });
  }

  // 为每个mint查找潜在的市场池
  console.log('  Mint对应的潜在市场池:');
  for (const mint of mints) {
    if (mint !== 'So11111111111111111111111111111111111111112') {
      const symbol = KNOWN_TOKENS[mint] || mint.slice(0, 8);
      console.log(`    ${symbol} (${mint}):`);
      console.log(`      - Pump.fun AMM (${symbol}-WSOL) Market`);
      console.log(`      - Meteora (${symbol}-WSOL) Market`);
      console.log(`      - Raydium (${symbol}-WSOL) Pool`);
      console.log(`      - Orca (${symbol}-WSOL) Pool`);
    }
  }
  console.log('');
}

/**
 * 从日志中提取市场池信息
 */
function extractMarketPoolsFromLogs(transaction) {
  const pools = [];
  
  if (!transaction.meta?.logMessages) return pools;

  for (const log of transaction.meta.logMessages) {
    // 检测Pump.fun相关日志
    if (log.includes('Pump.fun') || log.includes('6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P')) {
      pools.push({
        type: 'Pump.fun AMM',
        address: 'detected_from_logs',
        description: 'Pump.fun AMM Market detected from transaction logs'
      });
    }
    
    // 检测Meteora相关日志
    if (log.includes('Meteora') || log.includes('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')) {
      pools.push({
        type: 'Meteora DLMM',
        address: 'detected_from_logs',
        description: 'Meteora DLMM Market detected from transaction logs'
      });
    }
  }

  return pools;
}

/**
 * 分析交易流程
 */
function analyzeTransactionFlow(transaction) {
  console.log('🔄 交易流程分析:');
  
  if (transaction.meta?.logMessages) {
    console.log('  关键操作:');
    let stepCount = 1;
    
    for (const log of transaction.meta.logMessages) {
      if (log.includes('invoke [1]')) {
        const programMatch = log.match(/Program (\w+) invoke/);
        if (programMatch) {
          const programId = programMatch[1];
          const programName = KNOWN_PROGRAMS[programId] || programId.slice(0, 8);
          console.log(`    ${stepCount}. 调用 ${programName}`);
          stepCount++;
        }
      } else if (log.includes('Transfer')) {
        console.log(`    ${stepCount}. 代币转账操作`);
        stepCount++;
      } else if (log.includes('SolanaMevBot.com')) {
        console.log(`    ${stepCount}. 🤖 MEV Bot操作 (SolanaMevBot.com)`);
        stepCount++;
      }
    }
  }
  console.log('');
}

// 运行分析
if (require.main === module) {
  analyzeMevTransaction().catch(console.error);
}

module.exports = { analyzeMevTransaction };
