#!/usr/bin/env node

/**
 * 示例：分析特定交易
 * 
 * 这个脚本演示如何使用Solana Web3.js直接分析你提供的交易：
 * NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj
 */

const { Connection, PublicKey } = require('@solana/web3.js');

// 配置
const RPC_URL = 'https://api.mainnet-beta.solana.com';
const TRANSACTION_SIGNATURE = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';

async function analyzeTransaction() {
  console.log('🔍 开始分析交易...');
  console.log(`📝 交易签名: ${TRANSACTION_SIGNATURE}`);
  console.log(`🔗 Solscan: https://solscan.io/tx/${TRANSACTION_SIGNATURE}`);
  console.log('');

  try {
    const connection = new Connection(RPC_URL);
    
    // 获取交易详情
    console.log('📡 正在获取交易详情...');
    const transaction = await connection.getParsedTransaction(TRANSACTION_SIGNATURE, {
      maxSupportedTransactionVersion: 0
    });

    if (!transaction) {
      console.error('❌ 未找到交易');
      return;
    }

    console.log('✅ 交易获取成功');
    console.log('');

    // 分析基本信息
    console.log('📊 基本信息:');
    console.log(`  插槽: ${transaction.slot}`);
    console.log(`  区块时间: ${transaction.blockTime ? new Date(transaction.blockTime * 1000).toLocaleString() : '未知'}`);
    console.log(`  手续费: ${transaction.meta?.fee || 0} lamports`);
    console.log(`  状态: ${transaction.meta?.err ? '失败' : '成功'}`);
    console.log('');

    // 分析地址查找表
    if (transaction.transaction.message.addressTableLookups && 
        transaction.transaction.message.addressTableLookups.length > 0) {
      console.log('📋 地址查找表:');
      transaction.transaction.message.addressTableLookups.forEach((lookup, index) => {
        console.log(`  ${index + 1}. ${lookup.accountKey.toString()}`);
        console.log(`     可写索引: [${lookup.writableIndexes.join(', ')}]`);
        console.log(`     只读索引: [${lookup.readonlyIndexes.join(', ')}]`);
      });
      console.log('');
    } else {
      console.log('📋 地址查找表: 无');
      console.log('');
    }

    // 分析mint地址
    const mints = new Set();
    
    // 从preTokenBalances获取
    if (transaction.meta?.preTokenBalances) {
      transaction.meta.preTokenBalances.forEach(balance => {
        if (balance.mint) {
          mints.add(balance.mint);
        }
      });
    }

    // 从postTokenBalances获取
    if (transaction.meta?.postTokenBalances) {
      transaction.meta.postTokenBalances.forEach(balance => {
        if (balance.mint) {
          mints.add(balance.mint);
        }
      });
    }

    if (mints.size > 0) {
      console.log('🪙 Mint地址:');
      Array.from(mints).forEach((mint, index) => {
        console.log(`  ${index + 1}. ${mint}`);
        // 识别常见代币
        if (mint === 'So11111111111111111111111111111111111111112') {
          console.log('     代币: SOL (Wrapped)');
        } else if (mint === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v') {
          console.log('     代币: USDC');
        }
      });
      console.log('');
    } else {
      console.log('🪙 Mint地址: 无');
      console.log('');
    }

    // 分析程序调用
    console.log('🔧 程序调用:');
    try {
      const accountKeys = transaction.transaction.message.accountKeys;
      const instructions = transaction.transaction.message.instructions;

      instructions.forEach((instruction, index) => {
        try {
          const programKey = accountKeys[instruction.programIdIndex];
          let programId;

          if (typeof programKey === 'string') {
            programId = programKey;
          } else if (programKey && programKey.pubkey) {
            programId = programKey.pubkey.toString();
          } else if (programKey && programKey.toString) {
            programId = programKey.toString();
          } else {
            programId = 'Unknown';
          }

          console.log(`  ${index + 1}. ${programId}`);

          // 识别已知程序
          const knownPrograms = {
            '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P': 'Pump.fun',
            '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8': 'Raydium AMM',
            'CAMMCzo5YL8w4VFF8KVHrK22GGUQpMkFr9WeqATV9Uu': 'Raydium CLMM',
            'CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C': 'Raydium CP',
            'whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc': 'Orca Whirlpool',
            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo': 'Meteora DLMM',
            'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4': 'Jupiter',
            'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA': 'Token Program',
            'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL': 'Associated Token Program',
            '11111111111111111111111111111111': 'System Program'
          };

          if (knownPrograms[programId]) {
            console.log(`     程序: ${knownPrograms[programId]}`);
          }
        } catch (err) {
          console.log(`  ${index + 1}. 解析失败: ${err.message}`);
        }
      });
    } catch (error) {
      console.log('  程序调用解析失败:', error.message);
    }
    console.log('');

    // 分析日志消息
    if (transaction.meta?.logMessages && transaction.meta.logMessages.length > 0) {
      console.log('📝 日志消息 (前10条):');
      transaction.meta.logMessages.slice(0, 10).forEach((log, index) => {
        console.log(`  ${index + 1}. ${log}`);
      });
      if (transaction.meta.logMessages.length > 10) {
        console.log(`  ... 还有 ${transaction.meta.logMessages.length - 10} 条日志`);
      }
      console.log('');
    }

    console.log('✅ 分析完成');
    
  } catch (error) {
    console.error('❌ 分析失败:', error.message);
  }
}

// 运行分析
if (require.main === module) {
  analyzeTransaction().catch(console.error);
}

module.exports = { analyzeTransaction };
