{"name": "solana-tx-monitor", "version": "1.0.0", "description": "Solana transaction monitor for parsing mint, lookup tables, and market pools", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "npx esrun src/index.ts", "test": "jest"}, "keywords": ["solana", "blockchain", "transaction", "monitor", "defi", "dex"], "author": "", "license": "MIT", "dependencies": {"@solana/web3.js": "^1.87.6", "@triton-one/yellowstone-grpc": "^1.3.0", "axios": "^1.6.0", "bs58": "^5.0.0", "dotenv": "^16.5.0"}, "devDependencies": {"@types/bs58": "^4.0.4", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.3.0"}}