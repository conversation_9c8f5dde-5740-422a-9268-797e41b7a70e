# MEV交易监听器

## 🎯 功能

- ✅ **监听特定地址**的所有交易
- ✅ **检查MEV合约**：只处理包含MEV合约的交易
- ✅ **精确提取**mint和市场地址
- ✅ **智能分组**：每个Pump.fun程序形成独立组
- ✅ **数据限制**：最多保留10个mint数据

## 🚀 快速开始

```bash
# 测试模式
node start.js test

# 监听指定地址的MEV交易
node start.js [你要监听的地址]

# 示例：监听某个地址
node start.js 7dJjhC7ehSF9XKHyGZJGwXiFPm6Ss5NPrG7NZpgWt4n3

# 帮助
node start.js --help
```

## 📁 核心文件

```
├── start.js                    # 🚀 主启动脚本
├── simple-mev-monitor.js       # 📊 核心分析器
├── live-mev-monitor.js         # 🔴 实时监听器
└── package.json                # 📦 项目配置
```

## 🎯 工作原理

### 1. 监听流程
```
监听特定地址 → 检查交易 → 发现MEV合约 → 解析提取 → 输出结果
```

### 2. MEV合约检查
- **MEV合约**: `MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz`
- **检查逻辑**: 遍历交易指令，查找是否包含MEV合约
- **过滤机制**: 只处理包含MEV合约的交易

### 3. 分组规律
每个Pump.fun程序形成一个独立组：
- **Mint地址** = Pump.fun程序位置 - 2
- **Pump.fun Market** = Pump.fun程序位置 + 4  
- **Meteora Markets** = 组范围内的Meteora程序 + 2

## 📊 输出示例

```
📡 检测到地址交易: NN6VvyzS...gx7ENj
   插槽: 295024847
   时间: 2024-01-01 12:00:00
   🎯 发现包含MEV合约的交易！

🎯 处理MEV指令 3
   发现 2 个Pump.fun程序

   === 组 1 ===
   🪙 Mint: 39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73
   🟢 Pump.fun市场: BdjfXqH2MDPhuoaMUgKPy4RwF6sStUb8nDqjrJGgDUaC
   🟠 Meteora市场: JDfEHyw1sGWNrRGvjPE5uFKatPkyVViRcaveVp1FDcDc

   === 组 2 ===
   🪙 Mint: 766ivvadp4arnHKQ13RB3cD7PyvRDL42N2j7RCoMpump
   🟢 Pump.fun市场: Cg4T8JXfSXVC1sAYzKdntu9kUnd5i3CVxAgoyBK7hXyH
   🟠 Meteora市场: 22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH

   ✅ MEV交易分析成功!
   📊 统计: 2个mint, 6个市场

📊 Mint数据库状态: 2/10
```

## 🔧 程序化使用

```javascript
const { LiveMevMonitor } = require('./live-mev-monitor.js');

const monitor = new LiveMevMonitor('https://api.mainnet-beta.solana.com', {
  maxMints: 10,
  targetAddress: '你要监听的地址'
});

monitor.onMevTransaction((signature, mapping) => {
  console.log(`检测到MEV交易: ${signature}`);
  
  Object.entries(mapping).forEach(([mint, info]) => {
    console.log(`Mint ${info.symbol}:`);
    info.markets.pump.forEach(market => {
      console.log(`  Pump.fun: ${market.address}`);
    });
    info.markets.meteora.forEach(market => {
      console.log(`  Meteora: ${market.address}`);
    });
  });
});

await monitor.startLiveMonitoring();
```

## ⚙️ 配置选项

```javascript
const options = {
  maxMints: 10,                    // 最多保留mint数量
  targetAddress: '你的地址',        // 要监听的地址
  rpcUrl: 'https://api.mainnet-beta.solana.com'
};
```

## 🎯 使用场景

### 1. 监听钱包地址
```bash
node start.js [钱包地址]
```
监听某个钱包的MEV交易活动

### 2. 监听合约地址
```bash
node start.js [合约地址]
```
监听某个合约的MEV相关交易

### 3. 监听市场地址
```bash
node start.js [市场地址]
```
监听某个市场的MEV套利活动

## ⚠️ 注意事项

1. **地址格式**: 确保提供有效的Solana地址（44字符）
2. **RPC限制**: 建议使用付费RPC服务
3. **内存管理**: 自动限制mint数据数量
4. **网络延迟**: 实时监听可能有1-3秒延迟
5. **过滤机制**: 只处理包含MEV合约的交易

## 🎉 验证结果

已成功提取的真实市场地址：
- `22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH` (Meteora Market)
- `BdjfXqH2MDPhuoaMUgKPy4RwF6sStUb8nDqjrJGgDUaC` (Pump.fun Market)

测试交易: `NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj`

- ✅ 检测到2个mint
- ✅ 提取到6个市场（2个Pump.fun + 4个Meteora）
- ✅ 正确分组，避免数据混合
- ✅ 验证了真实的市场地址
