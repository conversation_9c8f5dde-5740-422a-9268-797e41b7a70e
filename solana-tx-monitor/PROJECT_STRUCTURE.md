# 项目结构

## 📁 核心文件

```
solana-tx-monitor/
├── start.js                    # 🚀 主启动脚本
├── simple-mev-monitor.js       # 📊 核心MEV分析器
├── live-mev-monitor.js         # 🔴 实时监听器
├── package.json                # 📦 项目配置
├── MEV_MONITOR_README.md       # 📖 使用说明
├── MEV_GROUPING_PATTERN.md     # 🎯 分组规律详解
└── PROJECT_STRUCTURE.md        # 📁 项目结构说明
```

## 🎯 文件说明

### `start.js` - 主启动脚本
- **用途**: 程序入口，提供简单的命令行界面
- **功能**: 
  - 测试模式：分析单个MEV交易
  - 实时模式：监听新的MEV交易
  - 配置管理：mint数量限制等

### `simple-mev-monitor.js` - 核心分析器
- **用途**: MEV交易分析的核心逻辑
- **功能**:
  - 解析MEV交易结构
  - 提取mint-market分组
  - 管理mint数据库（限制数量）
  - 应用精确的位置提取规律

### `live-mev-monitor.js` - 实时监听器
- **用途**: 扩展核心分析器，添加实时监听功能
- **功能**:
  - WebSocket连接管理
  - 实时交易处理
  - 错误处理和重连
  - 统计信息收集

## 🔧 配置选项

### 默认配置
```javascript
const CONFIG = {
  maxMints: 10,                    // 最多保留的mint数量
  rpcUrl: 'https://api.mainnet-beta.solana.com',
  confirmationLevel: 'confirmed'   // 确认级别
};
```

### 可调整参数
- `maxMints`: 控制内存中保留的mint数据数量
- `rpcUrl`: Solana RPC端点
- `confirmationLevel`: 交易确认级别 (`processed`, `confirmed`, `finalized`)

## 🎯 核心算法

### MEV交易识别
1. 监听包含MEV合约的交易: `MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz`
2. 解析交易指令结构
3. 验证是否为真实的MEV操作

### 分组提取规律
```
每个Pump.fun程序形成一个独立组:
├── Mint地址: Pump.fun程序位置 - 2
├── Pump.fun Market: Pump.fun程序位置 + 4
└── Meteora Markets: 组范围内的Meteora程序 + 2
```

### 数据管理
- **FIFO队列**: 新mint替换最旧的mint
- **自动清理**: 超过限制时删除旧数据
- **统计跟踪**: 记录首次发现时间、更新次数等

## 🚀 运行方式

### 1. 测试模式
```bash
node start.js test
```
- 分析已知的MEV交易
- 验证提取逻辑正确性
- 显示详细的分组信息

### 2. 实时监听模式
```bash
node start.js
```
- 监听新的MEV交易
- 实时提取mint-market映射
- 自动管理mint数据库

### 3. 自定义测试
```bash
node start.js test [交易签名]
```
- 测试指定的交易
- 用于验证新的交易模式

## 📊 输出格式

### 交易分析结果
```
🎯 处理MEV指令 3
   发现 2 个Pump.fun程序

   === 组 1 ===
   🪙 Mint: 39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73
   🟢 Pump.fun市场: BdjfXqH2MDPhuoaMUgKPy4RwF6sStUb8nDqjrJGgDUaC
   🟠 Meteora市场: JDfEHyw1sGWNrRGvjPE5uFKatPkyVViRcaveVp1FDcDc
   🟠 Meteora市场: 7dJjhC7ehSF9XKHyGZJGwXiFPm6Ss5NPrG7NZpgWt4n3

   === 组 2 ===
   🪙 Mint: 766ivvadp4arnHKQ13RB3cD7PyvRDL42N2j7RCoMpump
   🟢 Pump.fun市场: Cg4T8JXfSXVC1sAYzKdntu9kUnd5i3CVxAgoyBK7hXyH
   🟠 Meteora市场: 22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH
   🟠 Meteora市场: BxFCxkobrnuikdXgmtyb5tPLAipBERqU5x6cYYjyDumH
```

### 数据库状态
```
📊 Mint数据库状态: 2/10
   1. 39zSVsSH (39zSVsSH...)
      首次发现: 2024-01-01 12:00:00
      更新次数: 1
      市场数量: 3个
```

## 🔧 扩展开发

### 添加新的DEX支持
1. 在`PROGRAM_IDS`中添加新的程序ID
2. 在`extractGroupsFromMevInstruction`中添加提取逻辑
3. 更新显示和统计逻辑

### 修改数据存储
1. 替换`Map`为数据库连接
2. 实现持久化存储
3. 添加查询和索引功能

### 增强监听功能
1. 添加WebSocket重连逻辑
2. 实现多RPC端点负载均衡
3. 添加交易过滤条件

## ⚠️ 注意事项

1. **RPC限制**: 免费RPC有请求限制，建议使用付费服务
2. **内存管理**: mint数量限制防止内存溢出
3. **网络延迟**: 实时监听可能有1-3秒延迟
4. **数据准确性**: 基于已验证的模式，新模式需要更新算法

## 🎉 成功案例

已验证的真实市场地址：
- `22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH` (Meteora TAP-WSOL Market)
- `BdjfXqH2MDPhuoaMUgKPy4RwF6sStUb8nDqjrJGgDUaC` (Pump.fun AP-WSOL Market)
- `JDfEHyw1sGWNrRGvjPE5uFKatPkyVViRcaveVp1FDcDc` (Meteora Market)
- `Cg4T8JXfSXVC1sAYzKdntu9kUnd5i3CVxAgoyBK7hXyH` (Pump.fun Market)
