#!/usr/bin/env node

/**
 * 调试智能提取功能
 */

const { Connection } = require('@solana/web3.js');

const RPC_URL = 'https://api.mainnet-beta.solana.com';
const TEST_TRANSACTION = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';

// 已知程序ID
const PROGRAM_IDS = {
  PUMP_NEW: 'pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA',
  METEORA: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
  TOKEN_PROGRAM: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
  SYSTEM_PROGRAM: '11111111111111111111111111111111',
  MEV_CONTRACT: 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz'
};

async function debugExtraction() {
  console.log('🔍 调试智能提取功能');
  console.log(`📝 测试交易: ${TEST_TRANSACTION}`);
  console.log('');

  try {
    const connection = new Connection(RPC_URL);
    
    // 获取交易
    const transaction = await connection.getParsedTransaction(TEST_TRANSACTION, {
      maxSupportedTransactionVersion: 0
    });

    if (!transaction) {
      console.error('❌ 交易未找到');
      return;
    }

    // 提取基本信息
    const accountKeys = transaction.transaction.message.accountKeys.map(ak => {
      if (typeof ak === 'string') return ak;
      if (ak.pubkey) return ak.pubkey.toString();
      return ak.toString();
    });

    const instructions = transaction.transaction.message.instructions;
    
    console.log('📊 交易分析:');
    console.log(`   账户数: ${accountKeys.length}`);
    console.log(`   指令数: ${instructions.length}`);
    console.log('');

    // 分析程序分布
    console.log('🔧 程序分析:');
    accountKeys.forEach((key, index) => {
      const programName = Object.keys(PROGRAM_IDS).find(name => PROGRAM_IDS[name] === key);
      if (programName) {
        console.log(`   #${index}: ${programName} (${key})`);
      }
    });
    console.log('');

    // 分析每个指令
    console.log('🔄 指令分析:');
    instructions.forEach((instruction, index) => {
      const programIndex = instruction.programIdIndex;
      const programId = accountKeys[programIndex];
      const programName = Object.keys(PROGRAM_IDS).find(name => PROGRAM_IDS[name] === programId) || (programId ? programId.slice(0, 8) : 'UNKNOWN');
      
      console.log(`   指令 ${index + 1}: ${programName}`);
      console.log(`     程序ID: ${programId}`);
      console.log(`     程序索引: ${programIndex}`);
      
      if (instruction.accounts) {
        console.log(`     使用账户: ${instruction.accounts.length}个`);
        
        // 显示前几个账户
        const sampleAccounts = instruction.accounts.slice(0, 5);
        sampleAccounts.forEach((accIndex, i) => {
          const account = accountKeys[accIndex];
          console.log(`       ${i + 1}. #${accIndex}: ${account.slice(0, 8)}...${account.slice(-8)}`);
        });
        
        if (instruction.accounts.length > 5) {
          console.log(`       ... 还有 ${instruction.accounts.length - 5} 个账户`);
        }
        
        // 检查是否是目标程序
        if (programId === PROGRAM_IDS.PUMP_NEW) {
          console.log('     🟢 这是Pump.fun指令!');
          
          // 提取潜在的市场地址
          const potentialMarkets = instruction.accounts
            .map(accIndex => accountKeys[accIndex])
            .filter(acc => isPotentialMarketAddress(acc));
          
          console.log(`     潜在市场地址: ${potentialMarkets.length}个`);
          potentialMarkets.forEach(market => {
            console.log(`       - ${market}`);
          });
        }
        
        if (programId === PROGRAM_IDS.METEORA) {
          console.log('     🟠 这是Meteora指令!');
          
          // 提取潜在的市场地址
          const potentialMarkets = instruction.accounts
            .map(accIndex => accountKeys[accIndex])
            .filter(acc => isPotentialMarketAddress(acc));
          
          console.log(`     潜在市场地址: ${potentialMarkets.length}个`);
          potentialMarkets.forEach(market => {
            console.log(`       - ${market}`);
          });
        }
      }
      
      console.log('');
    });

    // 手动实现提取逻辑
    console.log('🎯 手动提取测试:');
    
    const mints = ['39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73', '766ivvadp4arnHKQ13RB3cD7PyvRDL42N2j7RCoMpump'];
    const result = {};
    
    mints.forEach(mint => {
      result[mint] = { pump: [], meteora: [] };
    });

    instructions.forEach((instruction, index) => {
      const programIndex = instruction.programIdIndex;
      const programId = accountKeys[programIndex];
      
      if (instruction.accounts) {
        const usedAccounts = instruction.accounts.map(accIndex => accountKeys[accIndex]);
        
        // Pump.fun指令
        if (programId === PROGRAM_IDS.PUMP_NEW) {
          console.log(`   处理Pump.fun指令 ${index + 1}`);
          
          const potentialMarkets = usedAccounts.filter(acc => isPotentialMarketAddress(acc));
          console.log(`     找到 ${potentialMarkets.length} 个潜在市场`);
          
          potentialMarkets.forEach(market => {
            console.log(`       添加市场: ${market}`);
            mints.forEach(mint => {
              result[mint].pump.push(market);
            });
          });
        }
        
        // Meteora指令
        if (programId === PROGRAM_IDS.METEORA) {
          console.log(`   处理Meteora指令 ${index + 1}`);
          
          const potentialMarkets = usedAccounts.filter(acc => isPotentialMarketAddress(acc));
          console.log(`     找到 ${potentialMarkets.length} 个潜在市场`);
          
          potentialMarkets.forEach(market => {
            console.log(`       添加市场: ${market}`);
            mints.forEach(mint => {
              result[mint].meteora.push(market);
            });
          });
        }
      }
    });

    console.log('');
    console.log('📊 手动提取结果:');
    Object.entries(result).forEach(([mint, markets]) => {
      console.log(`🪙 ${mint}:`);
      console.log(`   Pump.fun: ${markets.pump.length}个`);
      console.log(`   Meteora: ${markets.meteora.length}个`);
      
      if (markets.pump.length > 0) {
        markets.pump.forEach(market => {
          console.log(`     🟢 ${market}`);
        });
      }
      
      if (markets.meteora.length > 0) {
        markets.meteora.forEach(market => {
          console.log(`     🟠 ${market}`);
        });
      }
      
      console.log('');
    });

  } catch (error) {
    console.error('❌ 调试失败:', error.message);
    console.error(error.stack);
  }
}

/**
 * 判断是否为潜在的市场地址
 */
function isPotentialMarketAddress(address) {
  if (!address || typeof address !== 'string') {
    return false;
  }

  // 基本长度检查
  if (address.length < 32 || address.length > 44) {
    return false;
  }

  // 排除已知的程序地址
  if (Object.values(PROGRAM_IDS).includes(address)) {
    return false;
  }

  // 排除SOL mint
  if (address === 'So11111111111111111111111111111111111111112') {
    return false;
  }

  // 排除常见的系统账户模式
  if (address.startsWith('1111111') || address.endsWith('1111111')) {
    return false;
  }

  return true;
}

// 运行调试
if (require.main === module) {
  debugExtraction().catch(console.error);
}

module.exports = { debugExtraction };
