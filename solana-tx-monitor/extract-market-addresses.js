#!/usr/bin/env node

/**
 * 提取Market地址分析器
 * 
 * 专门提取mint对应的Market地址，而不是Pool地址
 */

console.log('🎯 MEV交易Market地址提取');
console.log('合约: MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz');
console.log('');

// 基于你提供的截图，提取真实的Market地址
const mevTransactionMarkets = {
  // 交易1 - AP代币
  AP: {
    mint: '39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73',
    symbol: 'AP',
    markets: {
      pumpfun: {
        // 从截图#16看到的
        name: 'Pump.fun AMM (AP-WSOL) Market',
        address: '需要从实际交易中提取' // 这个需要从真实交易数据中获取
      },
      meteora: {
        // 从截图#24, #33看到的
        name: 'Meteora (AP-WSOL) Market', 
        address: '需要从实际交易中提取' // 这个需要从真实交易数据中获取
      }
    }
  },
  
  // 交易2 - TAP代币
  TAP: {
    mint: '5PBWkkui11zTUgAoEirQkrZybznoit21tfVHT2y25kgj',
    symbol: 'TAP',
    markets: {
      pumpfun: {
        // 从截图#46看到的
        name: 'Pump.fun AMM (TAP-WSOL) Market',
        address: '需要从实际交易中提取'
      },
      meteora: {
        // 从截图#54, #63看到的 - 你提供了真实地址！
        name: 'Meteora (TAP-WSOL) Market',
        address: '22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH' // ✅ 这是你提供的真实地址
      }
    }
  }
};

/**
 * 显示Market地址映射
 */
function displayMarketMapping() {
  console.log('🏪 Mint-to-Market地址映射:');
  console.log('');

  Object.entries(mevTransactionMarkets).forEach(([symbol, data]) => {
    console.log(`${symbol === 'AP' ? '1️⃣' : '2️⃣'} ${symbol}代币:`);
    console.log(`   🪙 Mint: ${data.mint}`);
    console.log('   📈 Markets:');
    
    Object.entries(data.markets).forEach(([dex, market]) => {
      const icon = dex === 'pumpfun' ? '🟢' : '🟠';
      console.log(`     ${icon} ${market.name}:`);
      if (market.address === '22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH') {
        console.log(`       ✅ ${market.address}`);
      } else {
        console.log(`       ❓ ${market.address}`);
      }
    });
    console.log('');
  });
}

/**
 * 生成正确的JSON映射
 */
function generateCorrectMapping() {
  const correctMapping = {
    mevContract: 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz',
    mintToMarketMapping: {
      // AP代币的市场地址
      '39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73': {
        symbol: 'AP',
        markets: {
          pumpfun: {
            name: 'Pump.fun AMM (AP-WSOL) Market',
            address: 'NEED_TO_EXTRACT_FROM_REAL_TRANSACTION'
          },
          meteora: {
            name: 'Meteora (AP-WSOL) Market',
            address: 'NEED_TO_EXTRACT_FROM_REAL_TRANSACTION'
          }
        }
      },
      
      // TAP代币的市场地址
      '5PBWkkui11zTUgAoEirQkrZybznoit21tfVHT2y25kgj': {
        symbol: 'TAP',
        markets: {
          pumpfun: {
            name: 'Pump.fun AMM (TAP-WSOL) Market',
            address: 'NEED_TO_EXTRACT_FROM_REAL_TRANSACTION'
          },
          meteora: {
            name: 'Meteora (TAP-WSOL) Market',
            address: '22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH' // ✅ 已确认
          }
        }
      }
    }
  };

  console.log('📄 正确的Mint-to-Market映射JSON:');
  console.log(JSON.stringify(correctMapping, null, 2));
}

/**
 * 分析Market vs Pool的区别
 */
function explainMarketVsPool() {
  console.log('🔍 Market vs Pool 区别说明:');
  console.log('');
  console.log('📈 Market (市场):');
  console.log('   - 是交易对的主要标识符');
  console.log('   - 包含价格信息和交易逻辑');
  console.log('   - 例如: Meteora (TAP-WSOL) Market');
  console.log('   - 地址: 22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH');
  console.log('');
  console.log('🏊 Pool (池子):');
  console.log('   - 是流动性存储的地方');
  console.log('   - 一个Market可能有多个Pool');
  console.log('   - 例如: Pool 1, Pool 2 等');
  console.log('   - 这些是具体的流动性池地址');
  console.log('');
  console.log('✅ 你需要的是Market地址，因为它是交易对的核心标识！');
}

/**
 * 提供实际的查询建议
 */
function provideSuggestions() {
  console.log('💡 如何获取完整的Market地址:');
  console.log('');
  console.log('1️⃣ 从真实交易中提取:');
  console.log('   - 解析交易的accountKeys');
  console.log('   - 查找标记为"Market"的账户');
  console.log('   - 提取对应的地址');
  console.log('');
  console.log('2️⃣ 通过API查询:');
  console.log('   - 使用Pump.fun API查询AP代币的市场');
  console.log('   - 使用Meteora API查询对应的市场');
  console.log('');
  console.log('3️⃣ 链上查询:');
  console.log('   - 通过程序账户查询');
  console.log('   - 根据mint地址查找相关的市场账户');
  console.log('');
  console.log('🎯 目前已确认的Market地址:');
  console.log('   TAP-WSOL (Meteora): 22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH ✅');
}

/**
 * 主函数
 */
function main() {
  displayMarketMapping();
  explainMarketVsPool();
  provideSuggestions();
  generateCorrectMapping();
  
  console.log('\n🎉 Market地址提取分析完成！');
  console.log('现在明确了需要提取Market地址而不是Pool地址。');
}

// 运行分析
if (require.main === module) {
  main();
}

module.exports = { 
  mevTransactionMarkets,
  displayMarketMapping,
  generateCorrectMapping
};
