#!/usr/bin/env node

/**
 * 分析MEV交易模式
 * 
 * 通过分析真实的MEV交易来理解mint-to-market的映射规律
 */

const { Connection } = require('@solana/web3.js');

const RPC_URL = 'https://api.mainnet-beta.solana.com';
const TRANSACTION_SIGNATURE = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';
const MEV_CONTRACT = 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz';

// 已知程序ID
const PROGRAM_IDS = {
  PUMP_NEW: 'pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA',
  PUMP_OLD: '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P',
  METEORA: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
  TOKEN_PROGRAM: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
  SYSTEM_PROGRAM: '11111111111111111111111111111111'
};

/**
 * 分析交易模式
 */
async function analyzeTransactionPattern() {
  console.log('🔍 分析MEV交易模式');
  console.log(`📝 交易: ${TRANSACTION_SIGNATURE}`);
  console.log(`🤖 MEV合约: ${MEV_CONTRACT}`);
  console.log('');

  try {
    const connection = new Connection(RPC_URL);
    
    // 获取交易详情
    const transaction = await connection.getParsedTransaction(TRANSACTION_SIGNATURE, {
      maxSupportedTransactionVersion: 0
    });

    if (!transaction) {
      console.error('❌ 未找到交易');
      return;
    }

    console.log('✅ 交易获取成功');
    console.log('');

    // 分析账户结构
    analyzeAccountStructure(transaction);

    // 分析mint模式
    const mints = analyzeMintPattern(transaction);

    // 分析市场模式
    analyzeMarketPattern(transaction, mints);

    // 分析程序调用模式
    analyzeProgramCallPattern(transaction);

    // 总结规律
    summarizePatterns(transaction, mints);

  } catch (error) {
    console.error('❌ 分析失败:', error.message);
  }
}

/**
 * 分析账户结构
 */
function analyzeAccountStructure(transaction) {
  console.log('📊 账户结构分析:');
  
  const accountKeys = transaction.transaction.message.accountKeys;
  console.log(`总账户数: ${accountKeys.length}`);
  
  // 分类账户
  const programs = [];
  const mints = [];
  const others = [];
  
  accountKeys.forEach((key, index) => {
    const keyStr = typeof key === 'string' ? key : key.pubkey?.toString() || key.toString();
    
    if (Object.values(PROGRAM_IDS).includes(keyStr)) {
      programs.push({ index, address: keyStr, type: 'PROGRAM' });
    } else if (keyStr.length === 44 && !keyStr.includes('1111111')) {
      // 可能的mint或市场地址
      others.push({ index, address: keyStr, type: 'POTENTIAL_MINT_OR_MARKET' });
    }
  });

  console.log(`程序账户: ${programs.length}个`);
  programs.forEach(prog => {
    const name = Object.keys(PROGRAM_IDS).find(key => PROGRAM_IDS[key] === prog.address);
    console.log(`  #${prog.index}: ${name} (${prog.address})`);
  });
  
  console.log(`其他账户: ${others.length}个`);
  console.log('');
}

/**
 * 分析mint模式
 */
function analyzeMintPattern(transaction) {
  console.log('🪙 Mint模式分析:');
  
  const mints = new Set();
  
  // 从token balances获取mint
  if (transaction.meta?.preTokenBalances) {
    transaction.meta.preTokenBalances.forEach(balance => {
      if (balance.mint && balance.mint !== 'So11111111111111111111111111111111111111112') {
        mints.add(balance.mint);
      }
    });
  }
  
  if (transaction.meta?.postTokenBalances) {
    transaction.meta.postTokenBalances.forEach(balance => {
      if (balance.mint && balance.mint !== 'So11111111111111111111111111111111111111112') {
        mints.add(balance.mint);
      }
    });
  }

  const mintArray = Array.from(mints);
  console.log(`检测到 ${mintArray.length} 个mint:`);
  mintArray.forEach((mint, index) => {
    console.log(`  ${index + 1}. ${mint}`);
  });
  console.log('');
  
  return mintArray;
}

/**
 * 分析市场模式
 */
function analyzeMarketPattern(transaction, mints) {
  console.log('🏪 市场模式分析:');
  
  const accountKeys = transaction.transaction.message.accountKeys;
  const instructions = transaction.transaction.message.instructions;
  
  // 分析指令中的账户使用模式
  console.log('指令分析:');
  instructions.forEach((instruction, index) => {
    const programIndex = instruction.programIdIndex;
    const programKey = accountKeys[programIndex];
    const programStr = typeof programKey === 'string' ? programKey : programKey.pubkey?.toString() || programKey.toString();
    
    const programName = Object.keys(PROGRAM_IDS).find(key => PROGRAM_IDS[key] === programStr) || 'UNKNOWN';
    
    console.log(`  指令 ${index + 1}: ${programName}`);
    console.log(`    使用账户: ${instruction.accounts?.length || 0}个`);
    
    // 分析账户使用模式
    if (instruction.accounts) {
      const usedAccounts = instruction.accounts.map(accIndex => {
        const account = accountKeys[accIndex];
        return typeof account === 'string' ? account : account.pubkey?.toString() || account.toString();
      });
      
      // 查找可能的市场账户
      usedAccounts.forEach((acc, accIndex) => {
        if (acc.length === 44 && !Object.values(PROGRAM_IDS).includes(acc) && acc !== 'So11111111111111111111111111111111111111112') {
          console.log(`      账户 ${accIndex}: ${acc.slice(0, 8)}...${acc.slice(-8)} (可能的市场/池子)`);
        }
      });
    }
  });
  console.log('');
}

/**
 * 分析程序调用模式
 */
function analyzeProgramCallPattern(transaction) {
  console.log('🔄 程序调用模式:');
  
  if (transaction.meta?.logMessages) {
    const programCalls = [];
    
    transaction.meta.logMessages.forEach(log => {
      if (log.includes('Program ') && log.includes(' invoke')) {
        const match = log.match(/Program (\w+) invoke/);
        if (match) {
          const programId = match[1];
          const programName = Object.keys(PROGRAM_IDS).find(key => PROGRAM_IDS[key] === programId) || programId.slice(0, 8);
          programCalls.push(programName);
        }
      }
    });
    
    console.log('程序调用序列:');
    programCalls.forEach((call, index) => {
      console.log(`  ${index + 1}. ${call}`);
    });
  }
  console.log('');
}

/**
 * 总结规律
 */
function summarizePatterns(transaction, mints) {
  console.log('🎯 规律总结:');
  console.log('');
  
  console.log('1️⃣ MEV交易特征:');
  console.log('   ✅ 包含MEV合约地址');
  console.log('   ✅ 同时调用多个DEX程序');
  console.log('   ✅ 涉及多个mint代币');
  console.log('   ✅ 大量的账户操作');
  console.log('');
  
  console.log('2️⃣ Mint-to-Market映射规律:');
  console.log('   📈 每个mint对应多个市场');
  console.log('   📈 市场地址出现在指令的账户列表中');
  console.log('   📈 可写账户通常是池子或市场地址');
  console.log('   📈 程序调用顺序反映了套利路径');
  console.log('');
  
  console.log('3️⃣ 识别策略:');
  console.log('   🔍 从token balances提取mint');
  console.log('   🔍 从指令账户中找到相关的市场地址');
  console.log('   🔍 根据程序ID判断DEX类型');
  console.log('   🔍 可写账户优先级更高（更可能是市场）');
  console.log('');
  
  console.log('4️⃣ 实现建议:');
  console.log('   💡 解析每个指令的账户列表');
  console.log('   💡 根据程序ID分组账户');
  console.log('   💡 交叉引用mint和账户地址');
  console.log('   💡 优先考虑可写账户作为市场地址');
  console.log('');
  
  // 生成伪代码
  console.log('5️⃣ 伪代码逻辑:');
  console.log('```');
  console.log('for each mint in transaction:');
  console.log('  for each instruction in transaction:');
  console.log('    if instruction.program == PUMP_PROGRAM:');
  console.log('      extract writable accounts as potential pump markets');
  console.log('    if instruction.program == METEORA_PROGRAM:');
  console.log('      extract writable accounts as potential meteora markets');
  console.log('  map mint -> [pump_markets, meteora_markets]');
  console.log('```');
}

// 运行分析
if (require.main === module) {
  analyzeTransactionPattern().catch(console.error);
}

module.exports = { analyzeTransactionPattern };
