#!/usr/bin/env node

/**
 * 简化的智能提取测试
 * 
 * 直接测试mint-to-market映射提取功能
 */

const { Connection } = require('@solana/web3.js');
const { PoolQueryService } = require('./dist/pool-query-service.js');

const RPC_URL = 'https://api.mainnet-beta.solana.com';
const TEST_TRANSACTION = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';

async function testSimpleExtraction() {
  console.log('🧪 简化的智能提取测试');
  console.log(`📝 测试交易: ${TEST_TRANSACTION}`);
  console.log('');

  try {
    const connection = new Connection(RPC_URL);
    const poolService = new PoolQueryService(RPC_URL);
    
    console.log('🔍 获取交易数据...');
    
    // 获取交易
    const transaction = await connection.getParsedTransaction(TEST_TRANSACTION, {
      maxSupportedTransactionVersion: 0
    });

    if (!transaction) {
      console.error('❌ 交易未找到');
      return;
    }

    console.log('✅ 交易获取成功');
    console.log('');

    // 提取基本信息
    const accountKeys = transaction.transaction.message.accountKeys.map(ak => {
      if (typeof ak === 'string') return ak;
      if (ak.pubkey) return ak.pubkey.toString();
      return ak.toString();
    });

    const instructions = transaction.transaction.message.instructions;
    
    // 提取mint
    const mints = [];
    if (transaction.meta?.preTokenBalances) {
      transaction.meta.preTokenBalances.forEach(balance => {
        if (balance.mint && balance.mint !== 'So11111111111111111111111111111111111111112') {
          if (!mints.find(m => m.mint === balance.mint)) {
            mints.push({
              mint: balance.mint,
              decimals: balance.uiTokenAmount?.decimals,
              source: 'preTokenBalances'
            });
          }
        }
      });
    }

    if (transaction.meta?.postTokenBalances) {
      transaction.meta.postTokenBalances.forEach(balance => {
        if (balance.mint && balance.mint !== 'So11111111111111111111111111111111111111112') {
          if (!mints.find(m => m.mint === balance.mint)) {
            mints.push({
              mint: balance.mint,
              decimals: balance.uiTokenAmount?.decimals,
              source: 'postTokenBalances'
            });
          }
        }
      });
    }

    console.log('📊 交易基本信息:');
    console.log(`   账户数: ${accountKeys.length}`);
    console.log(`   指令数: ${instructions.length}`);
    console.log(`   Mint数: ${mints.length}`);
    console.log('');

    console.log('🪙 检测到的Mint:');
    mints.forEach((mint, index) => {
      console.log(`   ${index + 1}. ${mint.mint}`);
    });
    console.log('');

    // 构造TransactionInfo对象
    const transactionInfo = {
      signature: TEST_TRANSACTION,
      slot: transaction.slot,
      timestamp: transaction.blockTime || Math.floor(Date.now() / 1000),
      accountKeys,
      instructions,
      logMessages: transaction.meta?.logMessages || [],
      addressLookupTables: [],
      mints,
      marketPools: []
    };

    console.log('🎯 开始智能提取...');
    
    // 调用智能提取方法
    const mintToMarketMapping = poolService.extractMintToMarketFromTransaction(transactionInfo);
    
    console.log('✅ 智能提取完成');
    console.log('');

    // 显示结果
    console.log('📊 提取结果:');
    const totalMarkets = Object.values(mintToMarketMapping).reduce((sum, markets) => sum + markets.length, 0);
    console.log(`   总计提取到 ${totalMarkets} 个市场`);
    console.log('');

    Object.entries(mintToMarketMapping).forEach(([mint, markets]) => {
      console.log(`🪙 ${mint}:`);
      if (markets.length === 0) {
        console.log('   ❌ 未找到市场');
      } else {
        const pumpMarkets = markets.filter(m => m.additionalInfo?.dexType === 'pump');
        const meteoraMarkets = markets.filter(m => m.additionalInfo?.dexType === 'meteora');

        if (pumpMarkets.length > 0) {
          console.log(`   🟢 Pump.fun市场 (${pumpMarkets.length}个):`);
          pumpMarkets.forEach(market => {
            console.log(`     - ${market.poolAddress}`);
          });
        }

        if (meteoraMarkets.length > 0) {
          console.log(`   🟠 Meteora市场 (${meteoraMarkets.length}个):`);
          meteoraMarkets.forEach(market => {
            console.log(`     - ${market.poolAddress}`);
          });
        }
      }
      console.log('');
    });

    // 验证结果
    console.log('🎉 测试完成!');
    console.log('');
    console.log('📋 结果验证:');
    
    if (totalMarkets > 0) {
      console.log('✅ 智能提取功能正常工作');
      console.log('✅ 成功从MEV交易中提取市场地址');
      console.log('✅ 可以区分Pump.fun和Meteora市场');
      
      // 检查是否有重复的市场地址
      const allAddresses = Object.values(mintToMarketMapping).flat().map(m => m.poolAddress);
      const uniqueAddresses = new Set(allAddresses);
      
      if (allAddresses.length === uniqueAddresses.size) {
        console.log('✅ 没有重复的市场地址');
      } else {
        console.log('⚠️  检测到重复的市场地址');
      }
      
    } else {
      console.log('❌ 智能提取功能可能有问题');
      console.log('❌ 未能提取到任何市场地址');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
  }
}

// 运行测试
if (require.main === module) {
  testSimpleExtraction().catch(console.error);
}

module.exports = { testSimpleExtraction };
