#!/usr/bin/env node

/**
 * 稳健的MEV分析器
 * 
 * 分析MEV交易模式，提取mint-to-market映射规律
 */

const { Connection } = require('@solana/web3.js');

const RPC_URL = 'https://api.mainnet-beta.solana.com';
const TRANSACTION_SIGNATURE = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';

// 已知程序ID
const PROGRAM_IDS = {
  PUMP_NEW: 'pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA',
  METEORA: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
  TOKEN_PROGRAM: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
  SYSTEM_PROGRAM: '11111111111111111111111111111111',
  MEV_CONTRACT: 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz'
};

/**
 * 安全获取账户地址
 */
function safeGetAddress(account) {
  try {
    if (typeof account === 'string') {
      return account;
    }
    if (account && account.pubkey) {
      return account.pubkey.toString();
    }
    if (account && typeof account.toString === 'function') {
      return account.toString();
    }
    return null;
  } catch (error) {
    return null;
  }
}

/**
 * 检查是否为有效的Solana地址
 */
function isValidSolanaAddress(address) {
  return address && typeof address === 'string' && address.length >= 32 && address.length <= 44;
}

/**
 * 分析MEV交易
 */
async function analyzeMevTransaction() {
  console.log('🔍 稳健的MEV交易分析');
  console.log(`📝 交易: ${TRANSACTION_SIGNATURE}`);
  console.log('');

  try {
    const connection = new Connection(RPC_URL);
    
    const transaction = await connection.getParsedTransaction(TRANSACTION_SIGNATURE, {
      maxSupportedTransactionVersion: 0
    });

    if (!transaction) {
      console.error('❌ 未找到交易');
      return;
    }

    console.log('✅ 交易获取成功');
    
    // 安全提取账户
    const accountKeys = [];
    if (transaction.transaction.message.accountKeys) {
      transaction.transaction.message.accountKeys.forEach((key, index) => {
        const address = safeGetAddress(key);
        if (address) {
          accountKeys.push({ index, address });
        }
      });
    }

    console.log(`📊 总账户数: ${accountKeys.length}`);
    console.log('');

    // 提取mint
    const mints = extractMints(transaction);
    displayMints(mints);

    // 分析程序分布
    analyzeProgramDistribution(accountKeys);

    // 分析指令模式
    analyzeInstructionPattern(transaction, accountKeys);

    // 生成映射策略
    generateMappingStrategy(mints, accountKeys);

  } catch (error) {
    console.error('❌ 分析失败:', error.message);
    console.error(error.stack);
  }
}

/**
 * 提取mint地址
 */
function extractMints(transaction) {
  const mints = new Set();
  
  try {
    // 从pre token balances提取
    if (transaction.meta?.preTokenBalances) {
      transaction.meta.preTokenBalances.forEach(balance => {
        if (balance.mint && balance.mint !== 'So11111111111111111111111111111111111111112') {
          mints.add(balance.mint);
        }
      });
    }
    
    // 从post token balances提取
    if (transaction.meta?.postTokenBalances) {
      transaction.meta.postTokenBalances.forEach(balance => {
        if (balance.mint && balance.mint !== 'So11111111111111111111111111111111111111112') {
          mints.add(balance.mint);
        }
      });
    }
  } catch (error) {
    console.warn('提取mint时出错:', error.message);
  }

  return Array.from(mints);
}

/**
 * 显示mint信息
 */
function displayMints(mints) {
  console.log('🪙 检测到的Mint:');
  if (mints.length === 0) {
    console.log('   ❌ 未检测到mint');
  } else {
    mints.forEach((mint, index) => {
      console.log(`   ${index + 1}. ${mint}`);
      console.log(`      简称: ${mint.slice(0, 8)}...${mint.slice(-8)}`);
    });
  }
  console.log('');
}

/**
 * 分析程序分布
 */
function analyzeProgramDistribution(accountKeys) {
  console.log('🔧 程序分布分析:');
  
  const programCounts = {};
  const otherAccounts = [];
  
  accountKeys.forEach(({ index, address }) => {
    const programName = Object.keys(PROGRAM_IDS).find(key => PROGRAM_IDS[key] === address);
    
    if (programName) {
      programCounts[programName] = (programCounts[programName] || 0) + 1;
      console.log(`   #${index}: ${programName} (${address.slice(0, 8)}...${address.slice(-8)})`);
    } else if (isValidSolanaAddress(address)) {
      otherAccounts.push({ index, address });
    }
  });

  console.log('');
  console.log('📈 程序统计:');
  Object.entries(programCounts).forEach(([program, count]) => {
    console.log(`   ${program}: ${count}次`);
  });
  
  console.log(`   其他账户: ${otherAccounts.length}个`);
  console.log('');
}

/**
 * 分析指令模式
 */
function analyzeInstructionPattern(transaction, accountKeys) {
  console.log('🔄 指令模式分析:');
  
  try {
    const instructions = transaction.transaction.message.instructions || [];
    console.log(`   总指令数: ${instructions.length}`);
    
    instructions.forEach((instruction, index) => {
      try {
        const programIndex = instruction.programIdIndex;
        const programAccount = accountKeys.find(acc => acc.index === programIndex);
        
        if (programAccount) {
          const programName = Object.keys(PROGRAM_IDS).find(key => 
            PROGRAM_IDS[key] === programAccount.address
          ) || programAccount.address.slice(0, 8);
          
          console.log(`   指令 ${index + 1}: ${programName}`);
          
          if (instruction.accounts && instruction.accounts.length > 0) {
            console.log(`     涉及账户: ${instruction.accounts.length}个`);
            
            // 显示相关账户
            const relatedAccounts = instruction.accounts
              .map(accIndex => accountKeys.find(acc => acc.index === accIndex))
              .filter(acc => acc && !Object.values(PROGRAM_IDS).includes(acc.address))
              .slice(0, 5); // 只显示前5个
            
            relatedAccounts.forEach(acc => {
              console.log(`       ${acc.address.slice(0, 8)}...${acc.address.slice(-8)}`);
            });
          }
        }
      } catch (error) {
        console.log(`   指令 ${index + 1}: 解析失败`);
      }
    });
  } catch (error) {
    console.warn('分析指令时出错:', error.message);
  }
  
  console.log('');
}

/**
 * 生成映射策略
 */
function generateMappingStrategy(mints, accountKeys) {
  console.log('🎯 Mint-to-Market映射策略:');
  console.log('');
  
  console.log('1️⃣ 基于此交易的观察:');
  console.log(`   🪙 检测到 ${mints.length} 个mint`);
  console.log(`   🔧 涉及 ${accountKeys.length} 个账户`);
  console.log(`   📈 包含 Pump.fun 和 Meteora 程序`);
  console.log('');
  
  console.log('2️⃣ 映射规律推断:');
  console.log('   ✅ 每个mint可能对应多个市场');
  console.log('   ✅ 市场地址出现在指令的账户列表中');
  console.log('   ✅ 不同DEX的市场地址分布在不同指令中');
  console.log('   ✅ 需要根据程序ID区分DEX类型');
  console.log('');
  
  console.log('3️⃣ 实现建议:');
  console.log('   💡 步骤1: 从token balances提取mint');
  console.log('   💡 步骤2: 遍历每个指令');
  console.log('   💡 步骤3: 根据程序ID识别DEX类型');
  console.log('   💡 步骤4: 提取指令中的非程序账户作为潜在市场');
  console.log('   💡 步骤5: 建立mint到市场的关联');
  console.log('');
  
  console.log('4️⃣ 过滤条件:');
  console.log('   🚫 排除已知程序地址');
  console.log('   🚫 排除系统账户');
  console.log('   🚫 排除SOL mint地址');
  console.log('   ✅ 保留44字符长度的地址');
  console.log('   ✅ 优先考虑可写账户');
  console.log('');
  
  console.log('5️⃣ 代码框架:');
  console.log('```javascript');
  console.log('function extractMintToMarketMapping(transaction) {');
  console.log('  const mints = extractMintsFromTokenBalances(transaction);');
  console.log('  const result = {};');
  console.log('  ');
  console.log('  mints.forEach(mint => {');
  console.log('    result[mint] = { pump: [], meteora: [] };');
  console.log('  });');
  console.log('  ');
  console.log('  transaction.instructions.forEach(instruction => {');
  console.log('    const programId = getInstructionProgramId(instruction);');
  console.log('    const accounts = getInstructionAccounts(instruction);');
  console.log('    ');
  console.log('    if (programId === PUMP_PROGRAM) {');
  console.log('      const markets = filterMarketAccounts(accounts);');
  console.log('      mints.forEach(mint => result[mint].pump.push(...markets));');
  console.log('    }');
  console.log('    ');
  console.log('    if (programId === METEORA_PROGRAM) {');
  console.log('      const markets = filterMarketAccounts(accounts);');
  console.log('      mints.forEach(mint => result[mint].meteora.push(...markets));');
  console.log('    }');
  console.log('  });');
  console.log('  ');
  console.log('  return result;');
  console.log('}');
  console.log('```');
}

// 运行分析
if (require.main === module) {
  analyzeMevTransaction().catch(console.error);
}

module.exports = { analyzeMevTransaction };
