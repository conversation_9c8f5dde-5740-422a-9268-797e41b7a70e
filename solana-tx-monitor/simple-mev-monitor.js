#!/usr/bin/env node

/**
 * 简化的MEV监听器
 * 
 * 基于验证过的精确提取逻辑
 */

const { Connection } = require('@solana/web3.js');

const RPC_URL = 'https://api.mainnet-beta.solana.com';

// 程序ID
const PROGRAM_IDS = {
  PUMP_NEW: 'pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA',
  METEORA: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
  MEV_CONTRACT: 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz'
};

class SimpleMevMonitor {
  constructor(rpcUrl, options = {}) {
    this.connection = new Connection(rpcUrl);
    this.isMonitoring = false;
    this.onMevTransactionCallback = null;
    this.onErrorCallback = null;

    // 配置选项
    this.maxMints = options.maxMints || 10; // 最多保留的mint数量
    this.mintDatabase = new Map(); // 存储mint数据的数据库
    this.mintOrder = []; // 记录mint的添加顺序
  }

  /**
   * 设置MEV交易回调
   */
  onMevTransaction(callback) {
    this.onMevTransactionCallback = callback;
  }

  /**
   * 设置错误回调
   */
  onError(callback) {
    this.onErrorCallback = callback;
  }

  /**
   * 分析特定交易
   */
  async analyzeTransaction(signature) {
    try {
      console.log(`🔍 分析MEV交易: ${signature}`);
      
      // 获取交易
      const transaction = await this.connection.getParsedTransaction(signature, {
        maxSupportedTransactionVersion: 0
      });

      if (!transaction) {
        throw new Error('交易未找到');
      }

      // 检查是否包含MEV合约
      const containsMev = this.checkMevContract(transaction);
      if (!containsMev) {
        console.log('⚠️  交易不包含MEV合约');
        return null;
      }

      // 提取mint
      const mints = this.extractMints(transaction);
      console.log(`🪙 检测到 ${mints.length} 个mint`);

      // 提取市场映射
      const mapping = this.extractMintToMarketMapping(transaction, mints);
      
      // 更新mint数据库
      this.updateMintDatabase(mapping);

      // 显示结果
      this.displayResults(signature, mapping);

      return mapping;

    } catch (error) {
      const errorMsg = `分析交易失败: ${error.message}`;
      console.error('❌', errorMsg);
      
      if (this.onErrorCallback) {
        this.onErrorCallback(new Error(errorMsg));
      }
      
      return null;
    }
  }

  /**
   * 检查交易是否包含MEV合约
   */
  checkMevContract(transaction) {
    const instructions = transaction.transaction.message.instructions;
    
    for (const instruction of instructions) {
      const programIdStr = instruction.programId ? instruction.programId.toString() : '';
      if (programIdStr === PROGRAM_IDS.MEV_CONTRACT) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * 提取mint地址
   */
  extractMints(transaction) {
    const mints = [];
    const seenMints = new Set();

    if (transaction.meta?.preTokenBalances) {
      transaction.meta.preTokenBalances.forEach(balance => {
        if (balance.mint && balance.mint !== 'So11111111111111111111111111111111111111112') {
          if (!seenMints.has(balance.mint)) {
            mints.push({
              mint: balance.mint,
              symbol: balance.mint.slice(0, 8),
              decimals: balance.uiTokenAmount?.decimals
            });
            seenMints.add(balance.mint);
          }
        }
      });
    }

    if (transaction.meta?.postTokenBalances) {
      transaction.meta.postTokenBalances.forEach(balance => {
        if (balance.mint && balance.mint !== 'So11111111111111111111111111111111111111112') {
          if (!seenMints.has(balance.mint)) {
            mints.push({
              mint: balance.mint,
              symbol: balance.mint.slice(0, 8),
              decimals: balance.uiTokenAmount?.decimals
            });
            seenMints.add(balance.mint);
          }
        }
      });
    }

    return mints;
  }

  /**
   * 提取mint-to-market映射（基于分组规律）
   */
  extractMintToMarketMapping(transaction, mints) {
    const mapping = {};

    // 分析指令
    const instructions = transaction.transaction.message.instructions;

    instructions.forEach((instruction, index) => {
      const programIdStr = instruction.programId ? instruction.programId.toString() : '';

      // 只处理MEV合约指令
      if (programIdStr === PROGRAM_IDS.MEV_CONTRACT) {
        console.log(`🎯 处理MEV指令 ${index + 1}`);

        const groups = this.extractGroupsFromMevInstruction(instruction);

        console.log(`   发现 ${groups.length} 个mint-market组`);

        // 为每个组创建映射
        groups.forEach((group, groupIndex) => {
          console.log(`   组 ${groupIndex + 1}: mint=${group.mint.slice(0, 8)}...`);

          mapping[group.mint] = {
            mint: group.mint,
            symbol: group.mint.slice(0, 8),
            decimals: undefined, // 从token balances中获取
            markets: {
              pump: group.pumpMarkets,
              meteora: group.meteoraMarkets
            }
          };
        });

        // 从token balances中补充mint信息
        this.enrichMintInfo(mapping, mints);
      }
    });

    return mapping;
  }

  /**
   * 从token balances中补充mint信息
   */
  enrichMintInfo(mapping, mints) {
    mints.forEach(mint => {
      if (mapping[mint.mint]) {
        mapping[mint.mint].symbol = mint.symbol || mapping[mint.mint].symbol;
        mapping[mint.mint].decimals = mint.decimals;
      }
    });
  }

  /**
   * 更新mint数据库（限制数量）
   */
  updateMintDatabase(mapping) {
    Object.entries(mapping).forEach(([mint, info]) => {
      // 如果mint已存在，更新数据
      if (this.mintDatabase.has(mint)) {
        this.mintDatabase.set(mint, {
          ...this.mintDatabase.get(mint),
          ...info,
          lastSeen: new Date(),
          updateCount: (this.mintDatabase.get(mint).updateCount || 0) + 1
        });
      } else {
        // 新mint，检查是否需要清理旧数据
        if (this.mintOrder.length >= this.maxMints) {
          // 删除最旧的mint
          const oldestMint = this.mintOrder.shift();
          this.mintDatabase.delete(oldestMint);
          console.log(`🗑️  删除旧mint数据: ${oldestMint.slice(0, 8)}... (保持${this.maxMints}个mint限制)`);
        }

        // 添加新mint
        this.mintDatabase.set(mint, {
          ...info,
          firstSeen: new Date(),
          lastSeen: new Date(),
          updateCount: 1
        });
        this.mintOrder.push(mint);
        console.log(`✅ 新增mint数据: ${info.symbol} (当前总数: ${this.mintDatabase.size})`);
      }
    });
  }

  /**
   * 获取当前mint数据库状态
   */
  getMintDatabase() {
    return {
      size: this.mintDatabase.size,
      maxSize: this.maxMints,
      mints: Array.from(this.mintDatabase.entries()).map(([mint, info]) => ({
        mint,
        symbol: info.symbol,
        firstSeen: info.firstSeen,
        lastSeen: info.lastSeen,
        updateCount: info.updateCount,
        pumpMarkets: info.markets.pump.length,
        meteoraMarkets: info.markets.meteora.length
      }))
    };
  }

  /**
   * 清空mint数据库
   */
  clearMintDatabase() {
    this.mintDatabase.clear();
    this.mintOrder = [];
    console.log('🗑️  已清空mint数据库');
  }

  /**
   * 从MEV指令中提取分组（每个Pump.fun程序形成一个组）
   */
  extractGroupsFromMevInstruction(instruction) {
    const groups = [];

    if (!instruction.accounts || !Array.isArray(instruction.accounts)) {
      return groups;
    }

    const accounts = instruction.accounts;

    // 找到所有Pump.fun程序的位置
    const pumpPositions = [];
    for (let i = 0; i < accounts.length; i++) {
      const accountStr = accounts[i] ? accounts[i].toString() : '';
      if (accountStr === PROGRAM_IDS.PUMP_NEW) {
        pumpPositions.push(i);
      }
    }

    console.log(`   发现 ${pumpPositions.length} 个Pump.fun程序`);

    // 为每个Pump.fun程序创建一个组
    pumpPositions.forEach((pumpPos, groupIndex) => {
      console.log(`\n   === 组 ${groupIndex + 1} ===`);

      // 提取mint (Pump.fun程序 - 2位)
      const mintIndex = pumpPos - 2;
      const mint = (mintIndex >= 0 && accounts[mintIndex]) ? accounts[mintIndex].toString() : '';

      // 提取Pump.fun市场 (Pump.fun程序 + 4位)
      const pumpMarketIndex = pumpPos + 4;
      const pumpMarket = (pumpMarketIndex < accounts.length && accounts[pumpMarketIndex]) ?
        accounts[pumpMarketIndex].toString() : '';

      console.log(`   🪙 Mint: ${mint} (位置 ${pumpPos}-2=${mintIndex})`);
      console.log(`   🟢 Pump.fun市场: ${pumpMarket} (位置 ${pumpPos}+4=${pumpMarketIndex})`);

      // 查找对应的Meteora市场
      // 在当前Pump.fun程序之后查找Meteora程序
      const meteoraMarkets = [];

      // 确定搜索范围：从当前Pump.fun程序到下一个Pump.fun程序（或结束）
      const nextPumpPos = groupIndex + 1 < pumpPositions.length ? pumpPositions[groupIndex + 1] : accounts.length;

      for (let i = pumpPos + 1; i < nextPumpPos; i++) {
        const accountStr = accounts[i] ? accounts[i].toString() : '';
        if (accountStr === PROGRAM_IDS.METEORA) {
          const meteoraMarketIndex = i + 2;
          if (meteoraMarketIndex < accounts.length) {
            const meteoraMarket = accounts[meteoraMarketIndex] ? accounts[meteoraMarketIndex].toString() : '';
            if (meteoraMarket) {
              meteoraMarkets.push({
                address: meteoraMarket,
                method: 'MEV_PATTERN_METEORA_+2',
                programPosition: i,
                marketPosition: meteoraMarketIndex
              });
              console.log(`   🟠 Meteora市场: ${meteoraMarket} (位置 ${i}+2=${meteoraMarketIndex})`);
            }
          }
        }
      }

      // 创建组
      if (mint && pumpMarket) {
        const group = {
          mint: mint,
          pumpMarkets: [{
            address: pumpMarket,
            method: 'MEV_PATTERN_PUMP_+4',
            programPosition: pumpPos,
            marketPosition: pumpMarketIndex
          }],
          meteoraMarkets: meteoraMarkets
        };

        groups.push(group);
        console.log(`   ✅ 组创建成功: 1个mint, 1个Pump.fun市场, ${meteoraMarkets.length}个Meteora市场`);
      } else {
        console.log(`   ❌ 组创建失败: mint=${!!mint}, pumpMarket=${!!pumpMarket}`);
      }
    });

    return groups;
  }

  /**
   * 显示分析结果
   */
  displayResults(signature, mapping) {
    console.log('\n📊 MEV交易分析结果:');
    console.log(`   交易: ${signature}`);
    console.log(`   Mint数量: ${Object.keys(mapping).length}`);
    
    let totalPumpMarkets = 0;
    let totalMeteoraMarkets = 0;
    
    Object.values(mapping).forEach(info => {
      totalPumpMarkets += info.markets.pump.length;
      totalMeteoraMarkets += info.markets.meteora.length;
    });
    
    console.log(`   Pump.fun市场: ${totalPumpMarkets}个`);
    console.log(`   Meteora市场: ${totalMeteoraMarkets}个`);
    console.log('');

    // 显示详细映射
    Object.entries(mapping).forEach(([mintAddress, info]) => {
      console.log(`🪙 ${info.symbol} (${mintAddress}):`);
      
      if (info.markets.pump.length > 0) {
        console.log(`   🟢 Pump.fun Markets:`);
        info.markets.pump.forEach((market, index) => {
          console.log(`     ${index + 1}. ${market.address}`);
          console.log(`        方法: ${market.method}`);
          console.log(`        位置: ${market.programPosition} → ${market.marketPosition}`);
        });
      }
      
      if (info.markets.meteora.length > 0) {
        console.log(`   🟠 Meteora Markets:`);
        info.markets.meteora.forEach((market, index) => {
          console.log(`     ${index + 1}. ${market.address}`);
          console.log(`        方法: ${market.method}`);
          console.log(`        位置: ${market.programPosition} → ${market.marketPosition}`);
        });
      }
      
      console.log('');
    });

    // 显示数据库状态
    const dbStatus = this.getMintDatabase();
    console.log(`📊 Mint数据库状态: ${dbStatus.size}/${dbStatus.maxSize}`);

    // 触发回调
    if (this.onMevTransactionCallback) {
      this.onMevTransactionCallback(signature, mapping);
    }
  }

  /**
   * 开始实时监听（简化版本）
   */
  async startMonitoring() {
    console.log('🚀 启动简化MEV监听器');
    console.log('⚠️  注意: 这是演示版本，实际部署需要使用WebSocket或gRPC');
    console.log('');
    
    // 这里可以实现实时监听逻辑
    // 例如使用 connection.onLogs() 或 WebSocket
    
    this.isMonitoring = true;
  }

  /**
   * 停止监听
   */
  stopMonitoring() {
    this.isMonitoring = false;
    console.log('🛑 MEV监听器已停止');
  }
}

// 测试函数
async function testSimpleMevMonitor() {
  console.log('🧪 测试简化MEV监听器');
  console.log('');

  const monitor = new SimpleMevMonitor(RPC_URL);
  
  // 设置回调
  monitor.onMevTransaction((signature, mapping) => {
    console.log('🎉 MEV交易回调触发!');
    console.log(`   交易: ${signature}`);
    console.log(`   检测到 ${Object.keys(mapping).length} 个mint的市场映射`);
  });

  monitor.onError((error) => {
    console.error('❌ 监听器错误:', error.message);
  });

  // 测试已知交易
  const testTx = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';
  const result = await monitor.analyzeTransaction(testTx);
  
  if (result) {
    console.log('✅ 测试成功!');
    
    // 验证已知市场
    const knownMarket = '22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH';
    let found = false;
    
    Object.values(result).forEach(info => {
      info.markets.meteora.forEach(market => {
        if (market.address === knownMarket) {
          found = true;
        }
      });
    });
    
    if (found) {
      console.log(`✅ 成功验证已知市场: ${knownMarket}`);
    } else {
      console.log(`❌ 未找到已知市场: ${knownMarket}`);
    }
    
  } else {
    console.log('❌ 测试失败');
  }
}

// 导出
module.exports = { SimpleMevMonitor };

// 如果直接运行
if (require.main === module) {
  testSimpleMevMonitor().catch(console.error);
}
