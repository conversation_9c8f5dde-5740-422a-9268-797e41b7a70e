#!/usr/bin/env node

/**
 * 简化的MEV监听器
 * 
 * 基于验证过的精确提取逻辑
 */

const { Connection } = require('@solana/web3.js');

const RPC_URL = 'https://api.mainnet-beta.solana.com';

// 程序ID
const PROGRAM_IDS = {
  PUMP_NEW: 'pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA',
  METEORA: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
  MEV_CONTRACT: 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz'
};

class SimpleMevMonitor {
  constructor(rpcUrl) {
    this.connection = new Connection(rpcUrl);
    this.isMonitoring = false;
    this.onMevTransactionCallback = null;
    this.onErrorCallback = null;
  }

  /**
   * 设置MEV交易回调
   */
  onMevTransaction(callback) {
    this.onMevTransactionCallback = callback;
  }

  /**
   * 设置错误回调
   */
  onError(callback) {
    this.onErrorCallback = callback;
  }

  /**
   * 分析特定交易
   */
  async analyzeTransaction(signature) {
    try {
      console.log(`🔍 分析MEV交易: ${signature}`);
      
      // 获取交易
      const transaction = await this.connection.getParsedTransaction(signature, {
        maxSupportedTransactionVersion: 0
      });

      if (!transaction) {
        throw new Error('交易未找到');
      }

      // 检查是否包含MEV合约
      const containsMev = this.checkMevContract(transaction);
      if (!containsMev) {
        console.log('⚠️  交易不包含MEV合约');
        return null;
      }

      // 提取mint
      const mints = this.extractMints(transaction);
      console.log(`🪙 检测到 ${mints.length} 个mint`);

      // 提取市场映射
      const mapping = this.extractMintToMarketMapping(transaction, mints);
      
      // 显示结果
      this.displayResults(signature, mapping);
      
      return mapping;

    } catch (error) {
      const errorMsg = `分析交易失败: ${error.message}`;
      console.error('❌', errorMsg);
      
      if (this.onErrorCallback) {
        this.onErrorCallback(new Error(errorMsg));
      }
      
      return null;
    }
  }

  /**
   * 检查交易是否包含MEV合约
   */
  checkMevContract(transaction) {
    const instructions = transaction.transaction.message.instructions;
    
    for (const instruction of instructions) {
      const programIdStr = instruction.programId ? instruction.programId.toString() : '';
      if (programIdStr === PROGRAM_IDS.MEV_CONTRACT) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * 提取mint地址
   */
  extractMints(transaction) {
    const mints = [];
    const seenMints = new Set();

    if (transaction.meta?.preTokenBalances) {
      transaction.meta.preTokenBalances.forEach(balance => {
        if (balance.mint && balance.mint !== 'So11111111111111111111111111111111111111112') {
          if (!seenMints.has(balance.mint)) {
            mints.push({
              mint: balance.mint,
              symbol: balance.mint.slice(0, 8),
              decimals: balance.uiTokenAmount?.decimals
            });
            seenMints.add(balance.mint);
          }
        }
      });
    }

    if (transaction.meta?.postTokenBalances) {
      transaction.meta.postTokenBalances.forEach(balance => {
        if (balance.mint && balance.mint !== 'So11111111111111111111111111111111111111112') {
          if (!seenMints.has(balance.mint)) {
            mints.push({
              mint: balance.mint,
              symbol: balance.mint.slice(0, 8),
              decimals: balance.uiTokenAmount?.decimals
            });
            seenMints.add(balance.mint);
          }
        }
      });
    }

    return mints;
  }

  /**
   * 提取mint-to-market映射
   */
  extractMintToMarketMapping(transaction, mints) {
    const mapping = {};
    
    // 初始化映射
    mints.forEach(mint => {
      mapping[mint.mint] = {
        mint: mint.mint,
        symbol: mint.symbol,
        decimals: mint.decimals,
        markets: {
          pump: [],
          meteora: []
        }
      };
    });

    // 分析指令
    const instructions = transaction.transaction.message.instructions;
    
    instructions.forEach((instruction, index) => {
      const programIdStr = instruction.programId ? instruction.programId.toString() : '';
      
      // 只处理MEV合约指令
      if (programIdStr === PROGRAM_IDS.MEV_CONTRACT) {
        console.log(`🎯 处理MEV指令 ${index + 1}`);
        
        const markets = this.extractMarketsFromMevInstruction(instruction);
        
        // 将市场分配给所有mint
        mints.forEach(mint => {
          markets.pump.forEach(market => {
            mapping[mint.mint].markets.pump.push(market);
          });
          markets.meteora.forEach(market => {
            mapping[mint.mint].markets.meteora.push(market);
          });
        });
      }
    });

    return mapping;
  }

  /**
   * 从MEV指令中提取市场地址
   */
  extractMarketsFromMevInstruction(instruction) {
    const markets = {
      pump: [],
      meteora: []
    };

    if (!instruction.accounts || !Array.isArray(instruction.accounts)) {
      return markets;
    }

    const accounts = instruction.accounts;
    
    // 应用精确的提取规律
    for (let i = 0; i < accounts.length; i++) {
      const account = accounts[i];
      const accountStr = account ? account.toString() : '';
      
      // Pump.fun程序 + 4位
      if (accountStr === PROGRAM_IDS.PUMP_NEW) {
        const marketIndex = i + 4;
        if (marketIndex < accounts.length) {
          const marketAddress = accounts[marketIndex] ? accounts[marketIndex].toString() : '';
          if (marketAddress) {
            markets.pump.push({
              address: marketAddress,
              method: 'MEV_PATTERN_PUMP_+4',
              programPosition: i,
              marketPosition: marketIndex
            });
            console.log(`   🟢 Pump.fun市场: ${marketAddress} (位置 ${i}+4=${marketIndex})`);
          }
        }
      }
      
      // Meteora程序 + 2位
      if (accountStr === PROGRAM_IDS.METEORA) {
        const marketIndex = i + 2;
        if (marketIndex < accounts.length) {
          const marketAddress = accounts[marketIndex] ? accounts[marketIndex].toString() : '';
          if (marketAddress) {
            markets.meteora.push({
              address: marketAddress,
              method: 'MEV_PATTERN_METEORA_+2',
              programPosition: i,
              marketPosition: marketIndex
            });
            console.log(`   🟠 Meteora市场: ${marketAddress} (位置 ${i}+2=${marketIndex})`);
          }
        }
      }
    }

    return markets;
  }

  /**
   * 显示分析结果
   */
  displayResults(signature, mapping) {
    console.log('\n📊 MEV交易分析结果:');
    console.log(`   交易: ${signature}`);
    console.log(`   Mint数量: ${Object.keys(mapping).length}`);
    
    let totalPumpMarkets = 0;
    let totalMeteoraMarkets = 0;
    
    Object.values(mapping).forEach(info => {
      totalPumpMarkets += info.markets.pump.length;
      totalMeteoraMarkets += info.markets.meteora.length;
    });
    
    console.log(`   Pump.fun市场: ${totalPumpMarkets}个`);
    console.log(`   Meteora市场: ${totalMeteoraMarkets}个`);
    console.log('');

    // 显示详细映射
    Object.entries(mapping).forEach(([mintAddress, info]) => {
      console.log(`🪙 ${info.symbol} (${mintAddress}):`);
      
      if (info.markets.pump.length > 0) {
        console.log(`   🟢 Pump.fun Markets:`);
        info.markets.pump.forEach((market, index) => {
          console.log(`     ${index + 1}. ${market.address}`);
          console.log(`        方法: ${market.method}`);
          console.log(`        位置: ${market.programPosition} → ${market.marketPosition}`);
        });
      }
      
      if (info.markets.meteora.length > 0) {
        console.log(`   🟠 Meteora Markets:`);
        info.markets.meteora.forEach((market, index) => {
          console.log(`     ${index + 1}. ${market.address}`);
          console.log(`        方法: ${market.method}`);
          console.log(`        位置: ${market.programPosition} → ${market.marketPosition}`);
        });
      }
      
      console.log('');
    });

    // 触发回调
    if (this.onMevTransactionCallback) {
      this.onMevTransactionCallback(signature, mapping);
    }
  }

  /**
   * 开始实时监听（简化版本）
   */
  async startMonitoring() {
    console.log('🚀 启动简化MEV监听器');
    console.log('⚠️  注意: 这是演示版本，实际部署需要使用WebSocket或gRPC');
    console.log('');
    
    // 这里可以实现实时监听逻辑
    // 例如使用 connection.onLogs() 或 WebSocket
    
    this.isMonitoring = true;
  }

  /**
   * 停止监听
   */
  stopMonitoring() {
    this.isMonitoring = false;
    console.log('🛑 MEV监听器已停止');
  }
}

// 测试函数
async function testSimpleMevMonitor() {
  console.log('🧪 测试简化MEV监听器');
  console.log('');

  const monitor = new SimpleMevMonitor(RPC_URL);
  
  // 设置回调
  monitor.onMevTransaction((signature, mapping) => {
    console.log('🎉 MEV交易回调触发!');
    console.log(`   交易: ${signature}`);
    console.log(`   检测到 ${Object.keys(mapping).length} 个mint的市场映射`);
  });

  monitor.onError((error) => {
    console.error('❌ 监听器错误:', error.message);
  });

  // 测试已知交易
  const testTx = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';
  const result = await monitor.analyzeTransaction(testTx);
  
  if (result) {
    console.log('✅ 测试成功!');
    
    // 验证已知市场
    const knownMarket = '22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH';
    let found = false;
    
    Object.values(result).forEach(info => {
      info.markets.meteora.forEach(market => {
        if (market.address === knownMarket) {
          found = true;
        }
      });
    });
    
    if (found) {
      console.log(`✅ 成功验证已知市场: ${knownMarket}`);
    } else {
      console.log(`❌ 未找到已知市场: ${knownMarket}`);
    }
    
  } else {
    console.log('❌ 测试失败');
  }
}

// 导出
module.exports = { SimpleMevMonitor };

// 如果直接运行
if (require.main === module) {
  testSimpleMevMonitor().catch(console.error);
}
