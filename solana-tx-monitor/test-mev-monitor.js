#!/usr/bin/env node

/**
 * 测试完整的MEV监听器
 */

const { MevMonitor } = require('./dist/mev-monitor.js');

const RPC_URL = 'https://api.mainnet-beta.solana.com';
const TEST_TRANSACTION = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';

async function testMevMonitor() {
  console.log('🧪 测试完整的MEV监听器');
  console.log('');

  try {
    // 创建MEV监听器
    const monitor = new MevMonitor(RPC_URL);
    
    // 设置回调函数
    monitor.onMevTransactionDetected((txInfo, mapping) => {
      console.log('🎉 MEV交易回调触发!');
      console.log(`   交易签名: ${txInfo.signature}`);
      console.log(`   Mint数量: ${Object.keys(mapping).length}`);
      
      Object.entries(mapping).forEach(([mint, info]) => {
        const totalMarkets = info.markets.pump.length + info.markets.meteora.length;
        console.log(`   ${info.symbol}: ${totalMarkets}个市场`);
      });
      console.log('');
    });

    monitor.onErrorOccurred((error) => {
      console.error('❌ MEV监听器错误:', error.message);
    });

    console.log('🔍 测试1: 手动分析已知的MEV交易');
    console.log('');
    
    // 手动分析测试交易
    const mapping = await monitor.analyzeTransaction(TEST_TRANSACTION);
    
    if (mapping) {
      console.log('✅ 手动分析成功!');
      console.log('');
      
      // 验证结果
      console.log('🔍 验证提取结果:');
      
      const mintCount = Object.keys(mapping).length;
      console.log(`   检测到 ${mintCount} 个mint`);
      
      let totalPumpMarkets = 0;
      let totalMeteoraMarkets = 0;
      
      Object.values(mapping).forEach(info => {
        totalPumpMarkets += info.markets.pump.length;
        totalMeteoraMarkets += info.markets.meteora.length;
      });
      
      console.log(`   Pump.fun市场: ${totalPumpMarkets}个`);
      console.log(`   Meteora市场: ${totalMeteoraMarkets}个`);
      console.log(`   总市场数: ${totalPumpMarkets + totalMeteoraMarkets}个`);
      
      // 检查是否包含已知的Meteora市场
      const knownMeteoraMarket = '22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH';
      let foundKnownMarket = false;
      
      Object.values(mapping).forEach(info => {
        info.markets.meteora.forEach(market => {
          if (market.address === knownMeteoraMarket) {
            foundKnownMarket = true;
          }
        });
      });
      
      if (foundKnownMarket) {
        console.log(`   ✅ 成功提取到已知市场: ${knownMeteoraMarket}`);
      } else {
        console.log(`   ❌ 未找到已知市场: ${knownMeteoraMarket}`);
      }
      
      console.log('');
      
      // 显示详细的映射信息
      console.log('📋 详细的Mint-to-Market映射:');
      Object.entries(mapping).forEach(([mint, info]) => {
        console.log(`\n🪙 ${info.symbol}:`);
        console.log(`   Mint: ${mint}`);
        console.log(`   精度: ${info.decimals || 'Unknown'}`);
        
        if (info.markets.pump.length > 0) {
          console.log(`   🟢 Pump.fun Markets:`);
          info.markets.pump.forEach((market, index) => {
            console.log(`     ${index + 1}. ${market.address}`);
            console.log(`        方法: ${market.method}`);
            if (market.programPosition !== undefined) {
              console.log(`        程序位置: ${market.programPosition} → 市场位置: ${market.marketPosition}`);
            }
          });
        }
        
        if (info.markets.meteora.length > 0) {
          console.log(`   🟠 Meteora Markets:`);
          info.markets.meteora.forEach((market, index) => {
            console.log(`     ${index + 1}. ${market.address}`);
            console.log(`        方法: ${market.method}`);
            if (market.programPosition !== undefined) {
              console.log(`        程序位置: ${market.programPosition} → 市场位置: ${market.marketPosition}`);
            }
          });
        }
      });
      
      console.log('\n🎯 测试结果总结:');
      
      if (mintCount >= 2) {
        console.log('✅ 成功检测到多个mint');
      } else {
        console.log('⚠️  检测到的mint数量较少');
      }
      
      if (totalPumpMarkets > 0 && totalMeteoraMarkets > 0) {
        console.log('✅ 成功提取到Pump.fun和Meteora市场');
      } else {
        console.log('⚠️  未能提取到所有类型的市场');
      }
      
      if (foundKnownMarket) {
        console.log('✅ 验证了已知市场地址的正确性');
      } else {
        console.log('⚠️  未能验证已知市场地址');
      }
      
      console.log('\n🎉 MEV监听器测试完成!');
      
      // 生成使用示例
      console.log('\n📖 使用示例:');
      console.log('```javascript');
      console.log('const monitor = new MevMonitor(RPC_URL);');
      console.log('');
      console.log('monitor.onMevTransactionDetected((txInfo, mapping) => {');
      console.log('  console.log(`检测到MEV交易: ${txInfo.signature}`);');
      console.log('  ');
      console.log('  Object.entries(mapping).forEach(([mint, info]) => {');
      console.log('    console.log(`Mint ${info.symbol}:`);');
      console.log('    info.markets.pump.forEach(market => {');
      console.log('      console.log(`  Pump.fun: ${market.address}`);');
      console.log('    });');
      console.log('    info.markets.meteora.forEach(market => {');
      console.log('      console.log(`  Meteora: ${market.address}`);');
      console.log('    });');
      console.log('  });');
      console.log('});');
      console.log('');
      console.log('monitor.startMonitoring();');
      console.log('```');
      
    } else {
      console.log('❌ 手动分析失败');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
  }
}

// 运行测试
if (require.main === module) {
  testMevMonitor().catch(console.error);
}

module.exports = { testMevMonitor };
