#!/usr/bin/env node

/**
 * 测试智能市场提取功能
 * 
 * 验证基于交易模式分析的mint-to-market映射提取
 */

const { TransactionParser } = require('./dist/transaction-parser.js');

const RPC_URL = 'https://api.mainnet-beta.solana.com';
const TEST_TRANSACTION = 'NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj';

async function testSmartExtraction() {
  console.log('🧪 测试智能市场提取功能');
  console.log(`📝 测试交易: ${TEST_TRANSACTION}`);
  console.log('');

  try {
    // 创建交易解析器
    const parser = new TransactionParser(RPC_URL);
    
    console.log('🔍 开始解析交易...');
    
    // 解析交易
    const result = await parser.parseTransactionBySignature(TEST_TRANSACTION);
    
    if (!result.success) {
      console.error('❌ 交易解析失败:', result.error);
      return;
    }

    const txInfo = result.data;
    console.log('✅ 交易解析成功');
    console.log('');

    // 显示基本信息
    console.log('📊 交易基本信息:');
    console.log(`   签名: ${txInfo.signature}`);
    console.log(`   插槽: ${txInfo.slot}`);
    console.log(`   时间: ${new Date(txInfo.timestamp * 1000).toLocaleString()}`);
    console.log(`   账户数: ${txInfo.accountKeys.length}`);
    console.log(`   指令数: ${txInfo.instructions.length}`);
    console.log('');

    // 显示检测到的mint
    console.log('🪙 检测到的Mint:');
    if (txInfo.mints.length === 0) {
      console.log('   ❌ 未检测到mint');
    } else {
      txInfo.mints.forEach((mint, index) => {
        console.log(`   ${index + 1}. ${mint.mint}`);
        if (mint.symbol) console.log(`      符号: ${mint.symbol}`);
        if (mint.decimals !== undefined) console.log(`      精度: ${mint.decimals}`);
      });
    }
    console.log('');

    // 显示智能提取的市场信息
    console.log('🎯 智能提取的市场信息:');
    if (txInfo.marketPools.length === 0) {
      console.log('   ❌ 未提取到市场信息');
    } else {
      console.log(`   ✅ 提取到 ${txInfo.marketPools.length} 个市场:`);
      
      // 按DEX分组显示
      const groupedByDex = {};
      txInfo.marketPools.forEach(pool => {
        const dexName = pool.additionalInfo?.dexType || pool.dex;
        if (!groupedByDex[dexName]) {
          groupedByDex[dexName] = [];
        }
        groupedByDex[dexName].push(pool);
      });

      Object.entries(groupedByDex).forEach(([dex, pools]) => {
        console.log(`\n   ${dex === 'pump' ? '🟢' : '🟠'} ${dex === 'pump' ? 'Pump.fun' : 'Meteora'} (${pools.length}个):`);
        pools.forEach((pool, index) => {
          console.log(`     ${index + 1}. ${pool.poolAddress}`);
          console.log(`        类型: ${pool.additionalInfo?.marketType || pool.poolType}`);
          console.log(`        TokenA: ${pool.tokenA}`);
          console.log(`        TokenB: ${pool.tokenB}`);
          if (pool.additionalInfo?.extractedFromTransaction) {
            console.log(`        🎯 从交易中智能提取`);
          }
        });
      });
    }
    console.log('');

    // 生成mint-to-market映射表
    console.log('🔗 Mint-to-Market映射表:');
    txInfo.mints.forEach(mint => {
      console.log(`\n   🪙 ${mint.mint}:`);
      
      // 找到相关的市场
      const relatedMarkets = txInfo.marketPools.filter(pool => 
        pool.tokenA === mint.mint || pool.tokenA === 'EXTRACTED_FROM_TRANSACTION'
      );
      
      if (relatedMarkets.length === 0) {
        console.log('     ❌ 未找到相关市场');
      } else {
        const pumpMarkets = relatedMarkets.filter(pool => 
          pool.additionalInfo?.dexType === 'pump' || pool.dex === 'PUMP'
        );
        const meteoraMarkets = relatedMarkets.filter(pool => 
          pool.additionalInfo?.dexType === 'meteora' || pool.dex === 'METEORA'
        );

        if (pumpMarkets.length > 0) {
          console.log(`     🟢 Pump.fun市场 (${pumpMarkets.length}个):`);
          pumpMarkets.forEach(market => {
            console.log(`       - ${market.poolAddress}`);
          });
        }

        if (meteoraMarkets.length > 0) {
          console.log(`     🟠 Meteora市场 (${meteoraMarkets.length}个):`);
          meteoraMarkets.forEach(market => {
            console.log(`       - ${market.poolAddress}`);
          });
        }
      }
    });

    // 验证结果
    console.log('\n🎉 智能提取测试完成!');
    console.log('\n📋 测试结果总结:');
    console.log(`   ✅ 成功解析交易`);
    console.log(`   ✅ 检测到 ${txInfo.mints.length} 个mint`);
    console.log(`   ✅ 提取到 ${txInfo.marketPools.length} 个市场`);
    
    const extractedMarkets = txInfo.marketPools.filter(pool => 
      pool.additionalInfo?.extractedFromTransaction
    );
    console.log(`   🎯 其中 ${extractedMarkets.length} 个是智能提取的`);

    if (extractedMarkets.length > 0) {
      console.log('\n✨ 智能提取功能正常工作！');
      console.log('现在可以从MEV交易中自动提取mint对应的真实市场地址了。');
    } else {
      console.log('\n⚠️  智能提取功能可能需要调整');
      console.log('请检查交易结构和提取逻辑。');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
  }
}

// 运行测试
if (require.main === module) {
  testSmartExtraction().catch(console.error);
}

module.exports = { testSmartExtraction };
