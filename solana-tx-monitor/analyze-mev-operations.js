#!/usr/bin/env node

/**
 * MEV操作分析脚本
 * 
 * 基于你提供的交易账户信息，分析MEV合约中的操作
 * 提取mint和对应的market信息
 */

console.log('🔍 MEV操作分析器');
console.log('分析MEV合约: MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz');
console.log('');

// 模拟交易1的账户信息（基于你提供的截图）
const transaction1Accounts = [
  'AP', // #10 - Account: AP (这是mint符号)
  '7o8Qf7w7YVXnskmdKRDAbyXsMQp3p3CtHodgwCVLP5B7', // #11 - Account (WRITABLE)
  'pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA', // #12 - Pump.fun AMM (PROGRAM)
  'ADyA8hdefvWN2dbGGWFotbzWxrAvLW83WG6QCVXvJKqw', // #13 - Account
  'GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR', // #14 - Account
  'Pump.fun AMM: Protocol Fee 8', // #15 - Pump.fun AMM: Protocol Fee 8
  'Pump.fun AMM (AP-WSOL) Market', // #16 - Pump.fun AMM (AP-WSOL) Market
  'Pump.fun AMM (AP-WSOL) Pool 1', // #17 - Pump.fun AMM (AP-WSOL) Pool 1 (WRITABLE)
  'Pump.fun AMM (AP-WSOL) Pool 2', // #18 - Pump.fun AMM (AP-WSOL) Pool 2 (WRITABLE)
  'DWpvfqzGWuVy9jVSKSShdM2733nrEsnnhsUStYbkj6Nn', // #19 - Account (WRITABLE)
  '7q8Zrv92HDSknBFqgbrjj1pNo4b8Fa7NhdgoNFCyQb3f', // #20 - Account (WRITABLE)
  '3rVHiySCBT57NJ2GL9d4eN7bZAYRGQ9iXfgpU6UArvHC', // #21 - Account
  'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // #22 - Meteora DLMM Program (PROGRAM)
  'D1ZN9Wj1fRSUGfCjhvnu1hqDMT7hzjzBBpi12nVnlYD6', // #23 - Account
  'Meteora (AP-WSOL) Market', // #24 - Meteora (AP-WSOL) Market (WRITABLE)
  'Meteora (AP-WSOL) Pool 1', // #25 - Meteora (AP-WSOL) Pool 1 (WRITABLE)
  'Meteora (AP-WSOL) Pool 2', // #26 - Meteora (AP-WSOL) Pool 2 (WRITABLE)
  'oYGxhHnkcn1mMxCBs4n6JCZS5Ko4hvQvBgDXiYtipNE', // #27 - Account (WRITABLE)
  'BgHzzMhdvCDC7QmCHQa4LcckpKQwrgq8g9CUkHijuk5C', // #28 - Account (WRITABLE)
  '9PmX3gQdoZDtaAgm37MuEPfEtarHX5YBqS1YBQFQnuL', // #29 - Account (WRITABLE)
  '62Kpje5NSEN2u9u8mEhPF78V5CmdckXNLy4KsxZA7ie8', // #30 - Account (WRITABLE)
  'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // #31 - Meteora DLMM Program (PROGRAM)
  'D1ZN9Wj1fRSUGfCjhvnu1hqDMT7hzjzBBpi12nVnlYD6', // #32 - Account
  'Meteora (AP-WSOL) Market', // #33 - Meteora (AP-WSOL) Market (WRITABLE)
  'Meteora (AP-WSOL) Pool 1', // #34 - Meteora (AP-WSOL) Pool 1 (WRITABLE)
  'Meteora (AP-WSOL) Pool 2' // #35 - Meteora (AP-WSOL) Pool 2 (WRITABLE)
];

// 模拟交易2的账户信息
const transaction2Accounts = [
  'TAP', // #40 - Account: TAP (这是mint符号)
  '5PBWkkui11zTUgAoEirQkrZybznoit21tfVHT2y25kgj', // #41 - Account (WRITABLE)
  'pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA', // #42 - Pump.fun AMM (PROGRAM)
  'ADyA8hdefvWN2dbGGWFotbzWxrAvLW83WG6QCVXvJKqw', // #43 - Account
  'GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR', // #44 - Account
  'Pump.fun AMM: Protocol Fee 8', // #45 - Pump.fun AMM: Protocol Fee 8
  'Pump.fun AMM (TAP-WSOL) Market', // #46 - Pump.fun AMM (TAP-WSOL) Market
  'Pump.fun AMM (TAP-WSOL) Pool 1', // #47 - Pump.fun AMM (TAP-WSOL) Pool 1 (WRITABLE)
  'Pump.fun AMM (TAP-WSOL) Pool 2', // #48 - Pump.fun AMM (TAP-WSOL) Pool 2 (WRITABLE)
  'DWpvfqzGWuVy9jVSKSShdM2733nrEsnnhsUStYbkj6Nn', // #49 - Account (WRITABLE)
  'FxpVevCFt93zabdYMs6Mmmb3gC3xTnpokW1mHU4D84T8', // #50 - Account (WRITABLE)
  'HiyVH6tkN7uECSqdb5BGmdhLFje3EiWmtatFeBRVwYuc', // #51 - Account
  'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // #52 - Meteora DLMM Program (PROGRAM)
  'D1ZN9Wj1fRSUGfCjhvnu1hqDMT7hzjzBBpi12nVnlYD6', // #53 - Account
  'Meteora (TAP-WSOL) Market', // #54 - Meteora (TAP-WSOL) Market (WRITABLE)
  'Meteora (TAP-WSOL) Pool 1', // #55 - Meteora (TAP-WSOL) Pool 1 (WRITABLE)
  'Meteora (TAP-WSOL) Pool 2', // #56 - Meteora (TAP-WSOL) Pool 2 (WRITABLE)
  '815X2A8MjCEhJtkz81cpQmmjEpsgoQ1wtqKhzixvY7uA', // #57 - Account (WRITABLE)
  'J2LXRV8GUHDReG4Q6xxX37io5wbpWfqKRHuqHwBTGEC', // #58 - Account (WRITABLE)
  'GmDMcGoygvQ4JhDLu6vJZstXAsCBsnwsjtveQ4jRWPsk', // #59 - Account (WRITABLE)
  'DfheKAB1mNQdPWiVAnTa1ANCNrkV5Yc8YmIJQTqESve', // #60 - Account (WRITABLE)
  'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // #61 - Meteora DLMM Program (PROGRAM)
  'D1ZN9Wj1fRSUGfCjhvnu1hqDMT7hzjzBBpi12nVnlYD6', // #62 - Account
  'Meteora (TAP-WSOL) Market', // #63 - Meteora (TAP-WSOL) Market (WRITABLE)
  'Meteora (TAP-WSOL) Pool 1', // #64 - Meteora (TAP-WSOL) Pool 1 (WRITABLE)
  'Meteora (TAP-WSOL) Pool 2' // #65 - Meteora (TAP-WSOL) Pool 2 (WRITABLE)
];

/**
 * 分析单个交易的MEV操作
 */
function analyzeMevTransaction(transactionName, accounts) {
  console.log(`📊 ${transactionName}:`);
  console.log('');

  // 提取mint信息
  const mints = extractMints(accounts);
  console.log('🪙 检测到的Mint:');
  mints.forEach((mint, index) => {
    console.log(`  ${index + 1}. ${mint.symbol} (${mint.address})`);
  });
  console.log('');

  // 提取市场信息
  const markets = extractMarkets(accounts);
  console.log('🏪 检测到的Markets:');
  markets.forEach((market, index) => {
    console.log(`  ${index + 1}. ${market.dex}: ${market.name}`);
    if (market.address) {
      console.log(`     地址: ${market.address}`);
    }
  });
  console.log('');

  // 提取池子信息
  const pools = extractPools(accounts);
  console.log('🏊 检测到的Pools:');
  pools.forEach((pool, index) => {
    console.log(`  ${index + 1}. ${pool.dex}: ${pool.name}`);
    if (pool.address) {
      console.log(`     地址: ${pool.address}`);
    }
  });
  console.log('');

  // 生成映射关系
  console.log('🔗 Mint-to-Market映射:');
  mints.forEach(mint => {
    console.log(`  ${mint.symbol} (${mint.address}):`);
    
    // 找到相关的市场
    const relatedMarkets = markets.filter(market => 
      market.name.includes(mint.symbol) || market.name.includes('WSOL')
    );
    
    relatedMarkets.forEach(market => {
      console.log(`    📈 ${market.dex} Market: ${market.name}`);
      
      // 找到相关的池子
      const relatedPools = pools.filter(pool => 
        pool.name.includes(mint.symbol) && pool.dex === market.dex
      );
      
      relatedPools.forEach(pool => {
        console.log(`      🏊 Pool: ${pool.name}`);
      });
    });
  });

  console.log('\n' + '='.repeat(80) + '\n');
}

/**
 * 提取mint信息
 */
function extractMints(accounts) {
  const mints = [];
  
  // 已知的mint映射
  const mintMapping = {
    'AP': '39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73',
    'TAP': '5PBWkkui11zTUgAoEirQkrZybznoit21tfVHT2y25kgj'
  };

  accounts.forEach(account => {
    if (mintMapping[account]) {
      mints.push({
        symbol: account,
        address: mintMapping[account]
      });
    }
  });

  return mints;
}

/**
 * 提取市场信息
 */
function extractMarkets(accounts) {
  const markets = [];
  
  accounts.forEach(account => {
    if (account.includes('Market')) {
      let dex = 'Unknown';
      if (account.includes('Pump.fun')) {
        dex = 'Pump.fun';
      } else if (account.includes('Meteora')) {
        dex = 'Meteora';
      }
      
      markets.push({
        dex: dex,
        name: account,
        address: account // 在实际情况下，这里应该是真实的地址
      });
    }
  });

  return markets;
}

/**
 * 提取池子信息
 */
function extractPools(accounts) {
  const pools = [];
  
  accounts.forEach(account => {
    if (account.includes('Pool')) {
      let dex = 'Unknown';
      if (account.includes('Pump.fun')) {
        dex = 'Pump.fun';
      } else if (account.includes('Meteora')) {
        dex = 'Meteora';
      }
      
      pools.push({
        dex: dex,
        name: account,
        address: account // 在实际情况下，这里应该是真实的地址
      });
    }
  });

  return pools;
}

/**
 * 主函数
 */
function main() {
  console.log('🎯 MEV合约操作分析结果:\n');
  
  // 分析第一笔交易
  analyzeMevTransaction('交易1 (AP代币)', transaction1Accounts);
  
  // 分析第二笔交易
  analyzeMevTransaction('交易2 (TAP代币)', transaction2Accounts);

  // 总结
  console.log('📋 总结:');
  console.log('✅ 检测到2笔MEV交易');
  console.log('✅ 涉及2个不同的代币: AP, TAP');
  console.log('✅ 涉及2个DEX: Pump.fun, Meteora');
  console.log('✅ 每个代币都有对应的Market和Pool');
  console.log('✅ 每笔交易都包含跨DEX操作');
  
  console.log('\n🔍 实际mint地址:');
  console.log('  AP:  39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73');
  console.log('  TAP: 5PBWkkui11zTUgAoEirQkrZybznoit21tfVHT2y25kgj');
  
  console.log('\n🎉 分析完成！');
}

// 运行分析
if (require.main === module) {
  main();
}

module.exports = { analyzeMevTransaction, extractMints, extractMarkets, extractPools };
