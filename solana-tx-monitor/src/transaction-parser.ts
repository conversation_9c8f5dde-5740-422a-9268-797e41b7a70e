import bs58 from 'bs58';
import { Connection, PublicKey } from '@solana/web3.js';
import { 
  TransactionInfo, 
  AddressLookupTableInfo, 
  MintInfo, 
  MarketPoolInfo,
  ParseResult,
  PROGRAM_IDS 
} from './types';
import { PoolIdentifier } from './pool-identifier';

export class TransactionParser {
  private connection: Connection;
  private poolIdentifier: PoolIdentifier;

  constructor(rpcUrl: string) {
    this.connection = new Connection(rpcUrl);
    this.poolIdentifier = new PoolIdentifier(rpcUrl);
  }

  /**
   * 根据交易签名解析交易
   */
  async parseTransactionBySignature(signature: string): Promise<ParseResult> {
    try {
      console.log(`🔍 正在解析交易: ${signature}`);

      // 获取交易数据
      const transaction = await this.connection.getParsedTransaction(signature, {
        maxSupportedTransactionVersion: 0
      });

      if (!transaction) {
        return { success: false, error: 'Transaction not found' };
      }

      // 转换为我们的格式
      const data = {
        transaction: {
          signature,
          slot: transaction.slot,
          blockTime: transaction.blockTime,
          message: transaction.transaction.message
        },
        meta: transaction.meta
      };

      // 使用现有的解析逻辑
      return await this.parseTransaction(data);
    } catch (error) {
      return {
        success: false,
        error: `Failed to parse transaction: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * 解析Yellowstone gRPC交易数据
   */
  async parseTransaction(data: any): Promise<ParseResult> {
    try {
      if (!data.transaction) {
        return { success: false, error: 'No transaction data' };
      }

      const txData = data.transaction;
      const metaData = data.meta;

      // 基础交易信息
      const signature = txData.signature || 'unknown';
      const slot = txData.slot || 0;
      const timestamp = txData.blockTime || Math.floor(Date.now() / 1000);

      // 解析账户密钥
      const accountKeys = txData.message.accountKeys.map((ak: any) => {
        if (typeof ak === 'string') return ak;
        if (ak.pubkey) return ak.pubkey.toString();
        if (ak.toString) return ak.toString();
        return String(ak);
      });

      // 解析指令
      const instructions = txData.message.instructions;
      
      // 解析日志消息
      const logMessages = metaData?.logMessages || [];

      // 解析地址查找表
      const addressLookupTables = await this.parseAddressLookupTables(txData.message);

      // 解析mint信息
      const mints = this.parseMints(metaData);

      // 识别市场池
      const marketPools = await this.identifyMarketPools(accountKeys, instructions, logMessages);

      // 智能提取mint-to-market映射（基于交易模式分析）
      const tempTransactionInfo: TransactionInfo = {
        signature,
        slot,
        timestamp,
        accountKeys,
        instructions,
        logMessages,
        addressLookupTables,
        mints,
        marketPools: marketPools // 临时的，会被更新
      };

      // 使用智能提取方法
      const mintToMarketMapping = this.poolIdentifier.extractMintToMarketFromTransaction(tempTransactionInfo);

      // 转换映射为MarketPoolInfo数组
      const extractedMarkets: MarketPoolInfo[] = [];
      Object.entries(mintToMarketMapping).forEach(([mint, markets]) => {
        if (Array.isArray(markets)) {
          markets.forEach((market: MarketPoolInfo) => {
            // 更新tokenA为实际的mint地址
            market.tokenA = mint;
            extractedMarkets.push(market);
          });
        }
      });

      // 合并所有市场信息
      const allMarketPools = [...marketPools, ...extractedMarkets];

      // 为每个mint查找额外的API市场信息（作为补充）
      for (const mint of mints) {
        if (mint.mint !== 'So11111111111111111111111111111111111111112') { // 跳过SOL
          try {
            const apiMarkets = await this.poolIdentifier.findPoolsByMint(mint.mint);
            allMarketPools.push(...apiMarkets);
          } catch (error) {
            console.warn(`API查询mint ${mint.mint} 失败:`, error);
          }
        }
      }

      const transactionInfo: TransactionInfo = {
        signature,
        slot,
        timestamp,
        accountKeys,
        instructions,
        logMessages,
        addressLookupTables,
        mints,
        marketPools: allMarketPools
      };

      return { success: true, transactionInfo };
    } catch (error) {
      return { 
        success: false, 
        error: `解析交易失败: ${error instanceof Error ? error.message : String(error)}` 
      };
    }
  }

  /**
   * 解析地址查找表
   */
  private async parseAddressLookupTables(message: any): Promise<AddressLookupTableInfo[]> {
    const lookupTables: AddressLookupTableInfo[] = [];

    if (message.addressTableLookups) {
      for (const lookup of message.addressTableLookups) {
        const tableInfo: AddressLookupTableInfo = {
          tableAddress: bs58.encode(lookup.accountKey),
          writableIndexes: lookup.writableIndexes || [],
          readonlyIndexes: lookup.readonlyIndexes || []
        };

        // 尝试解析查找表中的地址
        try {
          const resolvedAddresses = await this.resolveAddressLookupTable(tableInfo.tableAddress);
          tableInfo.resolvedAddresses = resolvedAddresses;
        } catch (error) {
          console.warn(`无法解析查找表 ${tableInfo.tableAddress}:`, error);
        }

        lookupTables.push(tableInfo);
      }
    }

    return lookupTables;
  }

  /**
   * 解析查找表中的地址
   */
  private async resolveAddressLookupTable(tableAddress: string): Promise<string[]> {
    try {
      const pubkey = new PublicKey(tableAddress);
      const accountInfo = await this.connection.getAccountInfo(pubkey);
      
      if (!accountInfo) {
        return [];
      }

      // 解析查找表数据结构
      const data = accountInfo.data;
      const LOOKUP_TABLE_META_SIZE = 56;
      
      if (data.length < LOOKUP_TABLE_META_SIZE) {
        return [];
      }

      const serializedAddressesLen = data.length - LOOKUP_TABLE_META_SIZE;
      const numAddresses = serializedAddressesLen / 32;
      
      const addresses: string[] = [];
      for (let i = 0; i < numAddresses; i++) {
        const start = LOOKUP_TABLE_META_SIZE + (i * 32);
        const addressBytes = data.slice(start, start + 32);
        addresses.push(bs58.encode(addressBytes));
      }

      return addresses;
    } catch (error) {
      console.warn(`解析查找表失败 ${tableAddress}:`, error);
      return [];
    }
  }

  /**
   * 解析mint信息
   */
  private parseMints(metaData: any): MintInfo[] {
    const mints: MintInfo[] = [];
    const seenMints = new Set<string>();

    // 从preTokenBalances解析
    if (metaData?.preTokenBalances) {
      for (const balance of metaData.preTokenBalances) {
        if (balance.mint && !seenMints.has(balance.mint)) {
          mints.push({
            mint: balance.mint,
            decimals: balance.uiTokenAmount?.decimals,
            source: 'preTokenBalances',
            amount: balance.uiTokenAmount?.uiAmount,
            owner: balance.owner
          });
          seenMints.add(balance.mint);
        }
      }
    }

    // 从postTokenBalances解析
    if (metaData?.postTokenBalances) {
      for (const balance of metaData.postTokenBalances) {
        if (balance.mint && !seenMints.has(balance.mint)) {
          mints.push({
            mint: balance.mint,
            decimals: balance.uiTokenAmount?.decimals,
            source: 'postTokenBalances',
            amount: balance.uiTokenAmount?.uiAmount,
            owner: balance.owner
          });
          seenMints.add(balance.mint);
        }
      }
    }

    return mints;
  }

  /**
   * 识别市场池
   */
  private async identifyMarketPools(
    accountKeys: string[], 
    instructions: any[], 
    logMessages: string[]
  ): Promise<MarketPoolInfo[]> {
    return await this.poolIdentifier.identifyPools(accountKeys, instructions, logMessages);
  }
}
