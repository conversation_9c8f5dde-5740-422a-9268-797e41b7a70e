import { Connection, PublicKey } from '@solana/web3.js';
import { TransactionParser } from './transaction-parser';
import { MarketPoolInfo, TransactionInfo } from './types';

/**
 * MEV交易监听器
 * 
 * 专门监听包含MEV合约的交易，并提取mint-to-market映射
 */
export class MevMonitor {
  private connection: Connection;
  private parser: TransactionParser;
  private isMonitoring: boolean = false;
  
  // MEV合约地址
  private readonly MEV_CONTRACT = 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz';
  
  // 回调函数
  private onMevTransaction?: (txInfo: TransactionInfo, mintToMarketMapping: MintToMarketMapping) => void;
  private onError?: (error: Error) => void;

  constructor(rpcUrl: string) {
    this.connection = new Connection(rpcUrl);
    this.parser = new TransactionParser(rpcUrl);
  }

  /**
   * 设置MEV交易回调
   */
  onMevTransactionDetected(callback: (txInfo: TransactionInfo, mapping: MintToMarketMapping) => void) {
    this.onMevTransaction = callback;
  }

  /**
   * 设置错误回调
   */
  onErrorOccurred(callback: (error: Error) => void) {
    this.onError = callback;
  }

  /**
   * 开始监听MEV交易
   */
  async startMonitoring() {
    if (this.isMonitoring) {
      console.log('⚠️  MEV监听器已在运行');
      return;
    }

    this.isMonitoring = true;
    console.log('🚀 启动MEV交易监听器');
    console.log(`🎯 监听合约: ${this.MEV_CONTRACT}`);
    console.log('');

    try {
      // 订阅包含MEV合约的交易
      const mevPubkey = new PublicKey(this.MEV_CONTRACT);
      
      this.connection.onLogs(
        mevPubkey,
        (logs, context) => {
          this.handleMevTransaction(logs.signature, context.slot);
        },
        'confirmed'
      );

      console.log('✅ MEV监听器启动成功');
      
    } catch (error) {
      this.isMonitoring = false;
      const errorMsg = `启动MEV监听器失败: ${error instanceof Error ? error.message : String(error)}`;
      console.error('❌', errorMsg);
      
      if (this.onError) {
        this.onError(new Error(errorMsg));
      }
    }
  }

  /**
   * 停止监听
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      console.log('⚠️  MEV监听器未在运行');
      return;
    }

    this.isMonitoring = false;
    console.log('🛑 MEV监听器已停止');
  }

  /**
   * 处理MEV交易
   */
  private async handleMevTransaction(signature: string, slot: number) {
    try {
      console.log(`\n🎯 检测到MEV交易: ${signature}`);
      console.log(`   插槽: ${slot}`);
      
      // 解析交易
      const result = await this.parser.parseTransactionBySignature(signature);
      
      if (!result.success) {
        console.error(`❌ 解析MEV交易失败: ${result.error}`);
        return;
      }

      const txInfo = result.transactionInfo!;
      
      // 验证是否真的包含MEV合约
      const containsMevContract = this.verifyMevContract(txInfo);
      if (!containsMevContract) {
        console.log('⚠️  交易不包含MEV合约，跳过');
        return;
      }

      // 提取mint-to-market映射
      const mintToMarketMapping = this.extractMintToMarketMapping(txInfo);
      
      // 显示结果
      this.displayMevTransactionInfo(txInfo, mintToMarketMapping);
      
      // 调用回调函数
      if (this.onMevTransaction) {
        this.onMevTransaction(txInfo, mintToMarketMapping);
      }

    } catch (error) {
      const errorMsg = `处理MEV交易失败: ${error instanceof Error ? error.message : String(error)}`;
      console.error('❌', errorMsg);
      
      if (this.onError) {
        this.onError(new Error(errorMsg));
      }
    }
  }

  /**
   * 验证交易是否包含MEV合约
   */
  private verifyMevContract(txInfo: TransactionInfo): boolean {
    // 检查指令中是否有MEV合约
    for (const instruction of txInfo.instructions) {
      if (instruction.programId === this.MEV_CONTRACT) {
        return true;
      }
    }
    return false;
  }

  /**
   * 提取mint-to-market映射
   */
  private extractMintToMarketMapping(txInfo: TransactionInfo): MintToMarketMapping {
    const mapping: MintToMarketMapping = {};
    
    // 初始化每个mint的映射
    txInfo.mints.forEach(mint => {
      mapping[mint.mint] = {
        mint: mint.mint,
        symbol: mint.symbol || mint.mint.slice(0, 8),
        decimals: mint.decimals,
        markets: {
          pump: [],
          meteora: []
        }
      };
    });

    // 从市场池中提取信息
    txInfo.marketPools.forEach(pool => {
      if (pool.additionalInfo?.extractedFromTransaction) {
        const dexType = pool.additionalInfo.dexType;
        
        // 为每个mint添加市场信息
        Object.keys(mapping).forEach(mintAddress => {
          if (dexType === 'pump') {
            mapping[mintAddress].markets.pump.push({
              address: pool.poolAddress,
              method: pool.additionalInfo?.extractionMethod || 'UNKNOWN',
              programPosition: pool.additionalInfo?.programPosition,
              marketPosition: pool.additionalInfo?.marketPosition
            });
          } else if (dexType === 'meteora') {
            mapping[mintAddress].markets.meteora.push({
              address: pool.poolAddress,
              method: pool.additionalInfo?.extractionMethod || 'UNKNOWN',
              programPosition: pool.additionalInfo?.programPosition,
              marketPosition: pool.additionalInfo?.marketPosition
            });
          }
        });
      }
    });

    return mapping;
  }

  /**
   * 显示MEV交易信息
   */
  private displayMevTransactionInfo(txInfo: TransactionInfo, mapping: MintToMarketMapping) {
    console.log('📊 MEV交易分析结果:');
    console.log(`   🪙 检测到 ${txInfo.mints.length} 个mint`);
    console.log(`   🏪 提取到 ${txInfo.marketPools.length} 个市场`);
    
    const extractedMarkets = txInfo.marketPools.filter(pool => 
      pool.additionalInfo?.extractedFromTransaction
    );
    console.log(`   🎯 其中 ${extractedMarkets.length} 个是精确提取的`);
    console.log('');

    // 显示每个mint的映射
    Object.entries(mapping).forEach(([mintAddress, info]) => {
      console.log(`🪙 ${info.symbol} (${mintAddress}):`);
      
      if (info.markets.pump.length > 0) {
        console.log(`   🟢 Pump.fun Markets (${info.markets.pump.length}个):`);
        info.markets.pump.forEach(market => {
          console.log(`     - ${market.address} (${market.method})`);
        });
      }
      
      if (info.markets.meteora.length > 0) {
        console.log(`   🟠 Meteora Markets (${info.markets.meteora.length}个):`);
        info.markets.meteora.forEach(market => {
          console.log(`     - ${market.address} (${market.method})`);
        });
      }
      
      console.log('');
    });
  }

  /**
   * 手动分析特定交易
   */
  async analyzeTransaction(signature: string): Promise<MintToMarketMapping | null> {
    try {
      console.log(`🔍 手动分析交易: ${signature}`);
      
      const result = await this.parser.parseTransactionBySignature(signature);
      
      if (!result.success) {
        console.error(`❌ 解析交易失败: ${result.error}`);
        return null;
      }

      const txInfo = result.transactionInfo!;
      
      if (!this.verifyMevContract(txInfo)) {
        console.log('⚠️  交易不包含MEV合约');
        return null;
      }

      const mapping = this.extractMintToMarketMapping(txInfo);
      this.displayMevTransactionInfo(txInfo, mapping);
      
      return mapping;
      
    } catch (error) {
      console.error('❌ 分析交易失败:', error);
      return null;
    }
  }
}

// 类型定义
export interface MintToMarketMapping {
  [mintAddress: string]: {
    mint: string;
    symbol: string;
    decimals?: number;
    markets: {
      pump: MarketInfo[];
      meteora: MarketInfo[];
    };
  };
}

export interface MarketInfo {
  address: string;
  method: string;
  programPosition?: number;
  marketPosition?: number;
}
