import dotenv from 'dotenv';
import { Config } from './types';

// 加载环境变量
dotenv.config();

export const config: Config = {
  yellowstoneGrpcUrl: process.env.YELLOWSTONE_GRPC_URL || 'https://test-grpc.chainbuff.com',
  yellowstoneGrpcToken: process.env.YELLOWSTONE_GRPC_TOKEN,
  solanaRpcUrl: process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
  targetAddress: process.env.TARGET_ADDRESS || 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz',
  outputDir: process.env.OUTPUT_DIR || './data',
  enableFileOutput: process.env.ENABLE_FILE_OUTPUT === 'true',
  enableConsoleOutput: process.env.ENABLE_CONSOLE_OUTPUT !== 'false',
  raydiumApiUrl: process.env.RAYDIUM_API_URL || 'https://api.raydium.io/v2/sdk/liquidity/mainnet.json',
  meteoraApiUrl: process.env.METEORA_API_URL || 'https://dlmm-api.meteora.ag/pair/all',
  shyftApiKey: process.env.SHYFT_API_KEY
};

// 验证配置
export function validateConfig(): void {
  if (!config.targetAddress) {
    throw new Error('TARGET_ADDRESS is required');
  }
  
  if (!config.yellowstoneGrpcUrl) {
    throw new Error('YELLOWSTONE_GRPC_URL is required');
  }
  
  if (!config.solanaRpcUrl) {
    throw new Error('SOLANA_RPC_URL is required');
  }
  
  console.log('配置验证通过');
  console.log(`目标地址: ${config.targetAddress}`);
  console.log(`Yellowstone gRPC: ${config.yellowstoneGrpcUrl}`);
  console.log(`Solana RPC: ${config.solanaRpcUrl}`);
}
