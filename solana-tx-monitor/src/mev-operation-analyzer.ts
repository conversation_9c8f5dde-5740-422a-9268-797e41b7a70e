import { Connection } from '@solana/web3.js';
import { TransactionInfo, MarketPoolInfo, MintInfo } from './types';

/**
 * MEV操作分析器
 * 专门分析MEV合约中的操作，提取mint和对应的market信息
 */
export class MevOperationAnalyzer {
  private connection: Connection;

  constructor(rpcUrl: string) {
    this.connection = new Connection(rpcUrl);
  }

  /**
   * 分析MEV交易操作
   */
  analyzeMevOperation(transactionInfo: TransactionInfo): MevOperationResult {
    const result: MevOperationResult = {
      mevContract: 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz',
      operations: [],
      summary: {
        totalOperations: 0,
        uniqueMints: 0,
        involvedDEXs: [],
        totalPools: 0
      }
    };

    // 从账户密钥中识别操作
    const operations = this.extractOperationsFromAccounts(transactionInfo.accountKeys);
    result.operations = operations;

    // 生成摘要
    result.summary = this.generateSummary(operations);

    return result;
  }

  /**
   * 从账户密钥中提取操作信息
   */
  private extractOperationsFromAccounts(accountKeys: string[]): MevOperation[] {
    const operations: MevOperation[] = [];
    let currentOperation: Partial<MevOperation> | null = null;

    for (let i = 0; i < accountKeys.length; i++) {
      const account = accountKeys[i];

      // 检测代币mint
      if (this.isPotentialMint(account)) {
        // 开始新的操作
        if (currentOperation && currentOperation.mint) {
          operations.push(currentOperation as MevOperation);
        }
        
        currentOperation = {
          mint: account,
          symbol: this.getMintSymbol(account),
          markets: [],
          pools: []
        };
      }

      // 检测Pump.fun相关账户
      if (account.includes('Pump') || this.isPumpfunAccount(account, accountKeys, i)) {
        if (currentOperation) {
          const pumpMarket = this.extractPumpfunMarket(accountKeys, i);
          if (pumpMarket) {
            currentOperation.markets = currentOperation.markets || [];
            currentOperation.pools = currentOperation.pools || [];
            currentOperation.markets.push(pumpMarket.market);
            currentOperation.pools.push(...pumpMarket.pools);
          }
        }
      }

      // 检测Meteora相关账户
      if (account.includes('Meteora') || this.isMeteoraAccount(account, accountKeys, i)) {
        if (currentOperation) {
          const meteoraMarket = this.extractMeteoraMarket(accountKeys, i);
          if (meteoraMarket) {
            currentOperation.markets = currentOperation.markets || [];
            currentOperation.pools = currentOperation.pools || [];
            currentOperation.markets.push(meteoraMarket.market);
            currentOperation.pools.push(...meteoraMarket.pools);
          }
        }
      }
    }

    // 添加最后一个操作
    if (currentOperation && currentOperation.mint) {
      operations.push(currentOperation as MevOperation);
    }

    return operations;
  }

  /**
   * 检测是否为潜在的mint地址
   */
  private isPotentialMint(account: string): boolean {
    // 排除已知的非mint地址
    const knownNonMints = [
      'So11111111111111111111111111111111111111112', // SOL
      'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program
      'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL', // Associated Token Program
      '11111111111111111111111111111111', // System Program
      'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz' // MEV Contract
    ];

    if (knownNonMints.includes(account)) {
      return false;
    }

    // 简单的mint检测逻辑（可以根据需要完善）
    return account.length === 44 && !account.includes('Program') && !account.includes('Market') && !account.includes('Pool');
  }

  /**
   * 检测是否为Pump.fun相关账户
   */
  private isPumpfunAccount(account: string, accountKeys: string[], index: number): boolean {
    // 检查周围的账户是否有Pump.fun标识
    const context = accountKeys.slice(Math.max(0, index - 2), Math.min(accountKeys.length, index + 3));
    return context.some(acc => 
      acc.includes('pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA') || // Pump.fun程序
      acc.includes('Pump')
    );
  }

  /**
   * 检测是否为Meteora相关账户
   */
  private isMeteoraAccount(account: string, accountKeys: string[], index: number): boolean {
    const context = accountKeys.slice(Math.max(0, index - 2), Math.min(accountKeys.length, index + 3));
    return context.some(acc => 
      acc.includes('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo') || // Meteora程序
      acc.includes('Meteora')
    );
  }

  /**
   * 提取Pump.fun市场信息
   */
  private extractPumpfunMarket(accountKeys: string[], startIndex: number): { market: string, pools: string[] } | null {
    const pools: string[] = [];
    let market = '';

    // 在附近查找相关账户
    for (let i = Math.max(0, startIndex - 5); i < Math.min(accountKeys.length, startIndex + 10); i++) {
      const account = accountKeys[i];
      
      if (account.includes('Market')) {
        market = account;
      } else if (account.includes('Pool')) {
        pools.push(account);
      }
    }

    return market ? { market, pools } : null;
  }

  /**
   * 提取Meteora市场信息
   */
  private extractMeteoraMarket(accountKeys: string[], startIndex: number): { market: string, pools: string[] } | null {
    const pools: string[] = [];
    let market = '';

    for (let i = Math.max(0, startIndex - 5); i < Math.min(accountKeys.length, startIndex + 10); i++) {
      const account = accountKeys[i];
      
      if (account.includes('Market')) {
        market = account;
      } else if (account.includes('Pool')) {
        pools.push(account);
      }
    }

    return market ? { market, pools } : null;
  }

  /**
   * 获取mint符号
   */
  private getMintSymbol(mint: string): string {
    // 基于你提供的信息
    const knownMints: { [key: string]: string } = {
      'So11111111111111111111111111111111111111112': 'SOL',
      'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC',
      // 从图片中识别的mint
      '39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73': 'AP', // 第一笔交易
      '5PBWkkui11zTUgAoEirQkrZybznoit21tfVHT2y25kgj': 'TAP' // 第二笔交易
    };

    return knownMints[mint] || mint.slice(0, 8);
  }

  /**
   * 生成操作摘要
   */
  private generateSummary(operations: MevOperation[]): MevOperationSummary {
    const uniqueMints = new Set(operations.map(op => op.mint));
    const involvedDEXs = new Set<string>();
    let totalPools = 0;

    operations.forEach(op => {
      op.markets.forEach(market => {
        if (market.includes('Pump')) involvedDEXs.add('Pump.fun');
        if (market.includes('Meteora')) involvedDEXs.add('Meteora');
      });
      totalPools += op.pools.length;
    });

    return {
      totalOperations: operations.length,
      uniqueMints: uniqueMints.size,
      involvedDEXs: Array.from(involvedDEXs),
      totalPools
    };
  }
}

// 类型定义
export interface MevOperation {
  mint: string;
  symbol: string;
  markets: string[];
  pools: string[];
}

export interface MevOperationSummary {
  totalOperations: number;
  uniqueMints: number;
  involvedDEXs: string[];
  totalPools: number;
}

export interface MevOperationResult {
  mevContract: string;
  operations: MevOperation[];
  summary: MevOperationSummary;
}
