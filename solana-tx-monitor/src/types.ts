import { PublicKey } from '@solana/web3.js';

// 基础类型定义
export interface TransactionInfo {
  signature: string;
  slot: number;
  timestamp: number;
  accountKeys: string[];
  instructions: any[];
  logMessages: string[];
  addressLookupTables: AddressLookupTableInfo[];
  mints: MintInfo[];
  marketPools: MarketPoolInfo[];
}

// 地址查找表信息
export interface AddressLookupTableInfo {
  tableAddress: string;
  writableIndexes: number[];
  readonlyIndexes: number[];
  resolvedAddresses?: string[];
}

// Mint信息
export interface MintInfo {
  mint: string;
  decimals?: number;
  symbol?: string;
  name?: string;
  source: 'preTokenBalances' | 'postTokenBalances' | 'instruction';
  amount?: number;
  owner?: string;
}

// 市场池信息
export interface MarketPoolInfo {
  poolAddress: string;
  dex: DexType;
  tokenA: string;
  tokenB: string;
  poolType: PoolType;
  tvl?: number;
  volume24h?: number;
  fee?: number;
  additionalInfo?: any;
}

// DEX类型
export enum DexType {
  RAYDIUM = 'Raydium',
  ORCA = 'Orca',
  METEORA = 'Meteora',
  PUMP = 'Pump',
  JUPITER = 'Jupiter',
  WHIRLPOOL = 'Whirlpool',
  UNKNOWN = 'Unknown'
}

// 池子类型
export enum PoolType {
  AMM = 'AMM',
  CLMM = 'CLMM',
  DLMM = 'DLMM',
  STABLE = 'Stable',
  UNKNOWN = 'Unknown'
}

// 已知程序ID
export const PROGRAM_IDS = {
  RAYDIUM_AMM: '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8',
  RAYDIUM_CLMM: 'CAMMCzo5YL8w4VFF8KVHrK22GGUQpMkFr9WeqATV9Uu',
  RAYDIUM_CP: 'CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C',
  ORCA_WHIRLPOOL: 'whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc',
  METEORA_DLMM: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
  PUMP_OLD: '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P',
  PUMP: 'pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA',
  JUPITER: 'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4',
  TOKEN_PROGRAM: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
  ASSOCIATED_TOKEN_PROGRAM: 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL',
  SYSTEM_PROGRAM: '11111111111111111111111111111111'
};

// 配置接口
export interface Config {
  yellowstoneGrpcUrl: string;
  yellowstoneGrpcToken?: string;
  solanaRpcUrl: string;
  targetAddress: string;
  outputDir: string;
  enableFileOutput: boolean;
  enableConsoleOutput: boolean;
  raydiumApiUrl: string;
  meteoraApiUrl: string;
  shyftApiKey?: string;
}

// 监听器选项
export interface MonitorOptions {
  targetAddress: string;
  includeFailedTx?: boolean;
  includeVoteTx?: boolean;
  commitment?: 'processed' | 'confirmed' | 'finalized';
}

// 解析结果
export interface ParseResult {
  success: boolean;
  transactionInfo?: TransactionInfo;
  error?: string;
}
