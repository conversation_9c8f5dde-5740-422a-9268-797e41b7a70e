# 使用指南

## 快速开始

### 1. 配置环境变量

复制 `.env.example` 到 `.env` 并配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
# Yellowstone gRPC配置 (必需)
YELLOWSTONE_GRPC_URL=https://test-grpc.chainbuff.com
YELLOWSTONE_GRPC_TOKEN=your_token_here

# Solana RPC配置 (必需)
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

# 监听配置 (必需)
TARGET_ADDRESS=6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P

# 输出配置
OUTPUT_DIR=./data
ENABLE_FILE_OUTPUT=true
ENABLE_CONSOLE_OUTPUT=true
```

### 2. 运行项目

#### 开发模式（推荐）
```bash
npm run dev
```

#### 生产模式
```bash
npm run build
npm start
```

## 监听不同地址

### Pump.fun 交易监听
```env
TARGET_ADDRESS=6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P
```

### Raydium 交易监听
```env
TARGET_ADDRESS=675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8
```

### Jupiter 聚合器监听
```env
TARGET_ADDRESS=JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4
```

### 自定义钱包地址监听
```env
TARGET_ADDRESS=你的钱包地址
```

## 输出说明

### 控制台输出示例
```
================================================================================
🔍 新交易检测到
📝 签名: NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj
🎰 插槽: 310570418
⏰ 时间: 2024-01-15 10:30:45
🔗 Solscan: https://solscan.io/tx/NN6VvyzSC5XnDdqUgvm2cvS7ZaeSQDb57FbmiE6nz5oT6W7LWRJxHGYBpC6SPQjkD5obK1yPim1o9CZ25gx7ENj

📋 地址查找表 (2个):
  1. 3fxhSengmK5vjAWuM2tDUbnb2Y6dgVvPcbaboZCvD9tV
     解析地址数量: 15
  2. Eo9k25Go9VHFuQF6zgJ3fRm9SBqP8tG8Ea9sacowzyUv
     解析地址数量: 8

🪙 Mint地址 (3个):
  1. So11111111111111111111111111111111111111112
     代币: SOL
     精度: 9
  2. EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v
     代币: USDC
     精度: 6
  3. 4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R
     精度: 6

🏊 市场池 (2个):
  1. 58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2
     DEX: Raydium
     类型: CLMM
     代币A: So11111111111111111111111111111111111111112
     代币B: EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v
================================================================================
```

### 文件输出

#### JSONL格式 (`data/transactions_2024-01-15.jsonl`)
每行一个JSON对象，便于流式处理和分析。

#### 详细JSON格式 (`data/details/tx_<signature>.json`)
完整的交易解析结果，包含所有字段。

## 常见问题

### Q: 如何获取Yellowstone gRPC Token？
A: 联系Yellowstone gRPC服务提供商获取访问令牌。

### Q: 为什么没有检测到交易？
A: 检查以下几点：
1. 目标地址是否正确
2. Yellowstone gRPC连接是否正常
3. 该地址是否有活跃的交易

### Q: 如何分析特定的交易？
A: 使用你提供的交易签名，项目会自动解析其中的：
- Mint地址
- 地址查找表
- 市场池信息

### Q: 支持哪些DEX？
A: 目前支持：
- Raydium (AMM, CLMM, CP)
- Orca (Whirlpool)
- Meteora (DLMM)
- Pump.fun
- Jupiter (聚合器)

## 高级配置

### 自定义输出目录
```env
OUTPUT_DIR=./custom_data
```

### 禁用文件输出
```env
ENABLE_FILE_OUTPUT=false
```

### 禁用控制台输出
```env
ENABLE_CONSOLE_OUTPUT=false
```

## 故障排除

### 连接问题
1. 检查网络连接
2. 验证Yellowstone gRPC URL和Token
3. 确认Solana RPC URL可访问

### 性能优化
1. 使用高性能的Solana RPC节点
2. 调整输出设置减少I/O操作
3. 考虑使用SSD存储输出文件
