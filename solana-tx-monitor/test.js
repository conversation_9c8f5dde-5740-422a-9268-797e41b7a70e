// 简单的测试脚本来验证项目配置
const { TransactionMonitor } = require('./dist/index.js');

console.log('✅ 项目配置正确，可以正常导入模块');
console.log('🚀 Solana交易监听器已准备就绪');

console.log('\n📋 使用说明:');
console.log('1. 配置 .env 文件中的参数');
console.log('2. 运行 npm run dev 开始监听');
console.log('3. 或者运行 npm run build && npm start');

console.log('\n🔧 主要功能:');
console.log('- 实时监听指定地址的交易');
console.log('- 解析交易中的mint地址');
console.log('- 解析地址查找表(Address Lookup Tables)');
console.log('- 识别各种DEX的市场池');
console.log('- 支持文件和控制台输出');
