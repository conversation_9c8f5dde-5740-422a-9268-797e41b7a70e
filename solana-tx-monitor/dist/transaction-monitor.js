"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionMonitor = void 0;
const yellowstone_grpc_1 = __importStar(require("@triton-one/yellowstone-grpc"));
const transaction_parser_1 = require("./transaction-parser");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
class TransactionMonitor {
    constructor(grpcUrl, rpcUrl, outputDir = './data', enableFileOutput = true, enableConsoleOutput = true, grpcToken) {
        // 创建gRPC客户端
        const clientOptions = {
            "grpc.max_receive_message_length": 128 * 1024 * 1024, // 128MB
        };
        if (grpcToken) {
            // @ts-ignore
            clientOptions['grpc.metadata'] = { 'x-token': grpcToken };
        }
        // @ts-ignore
        this.client = new yellowstone_grpc_1.default(grpcUrl, undefined, clientOptions);
        this.parser = new transaction_parser_1.TransactionParser(rpcUrl);
        this.outputDir = outputDir;
        this.enableFileOutput = enableFileOutput;
        this.enableConsoleOutput = enableConsoleOutput;
        // 确保输出目录存在
        if (this.enableFileOutput) {
            this.ensureOutputDirectory();
        }
    }
    /**
     * 开始监听指定地址的交易
     */
    async startMonitoring(options) {
        try {
            console.log(`开始监听地址: ${options.targetAddress}`);
            console.log(`gRPC连接中...`);
            // 创建订阅数据流
            const stream = await this.client.subscribe();
            // 创建订阅请求
            const request = {
                accounts: {},
                slots: {},
                transactions: {
                    txn: {
                        vote: options.includeVoteTx || false,
                        failed: options.includeFailedTx || false,
                        signature: undefined,
                        accountInclude: [options.targetAddress],
                        accountExclude: [],
                        accountRequired: [],
                    }
                },
                transactionsStatus: {},
                blocks: {},
                blocksMeta: {},
                entry: {},
                accountsDataSlice: [],
                commitment: this.getCommitmentLevel(options.commitment || 'processed'),
                ping: undefined,
            };
            // 发送订阅请求
            await stream.write(request);
            console.log(`订阅请求已发送，开始监听交易...`);
            // 处理接收到的数据
            stream.on("data", async (data) => {
                if (data.transaction) {
                    await this.handleTransaction(data);
                }
            });
            // 处理错误
            stream.on("error", (error) => {
                console.error("gRPC流错误:", error);
            });
            // 处理连接结束
            stream.on("end", () => {
                console.log("gRPC流已结束");
            });
            // 保持连接
            this.keepAlive();
        }
        catch (error) {
            console.error("启动监听失败:", error);
            throw error;
        }
    }
    /**
     * 处理接收到的交易
     */
    async handleTransaction(data) {
        try {
            // 解析交易
            const parseResult = await this.parser.parseTransaction(data);
            if (!parseResult.success) {
                console.error("交易解析失败:", parseResult.error);
                return;
            }
            const transactionInfo = parseResult.transactionInfo;
            // 控制台输出
            if (this.enableConsoleOutput) {
                this.logTransactionToConsole(transactionInfo);
            }
            // 文件输出
            if (this.enableFileOutput) {
                await this.saveTransactionToFile(transactionInfo);
            }
        }
        catch (error) {
            console.error("处理交易时出错:", error);
        }
    }
    /**
     * 控制台输出交易信息
     */
    logTransactionToConsole(txInfo) {
        console.log('\n' + '='.repeat(80));
        console.log(`🔍 新交易检测到`);
        console.log(`📝 签名: ${txInfo.signature}`);
        console.log(`🎰 插槽: ${txInfo.slot}`);
        console.log(`⏰ 时间: ${new Date(txInfo.timestamp).toLocaleString()}`);
        console.log(`🔗 Solscan: https://solscan.io/tx/${txInfo.signature}`);
        // 地址查找表
        if (txInfo.addressLookupTables.length > 0) {
            console.log(`\n📋 地址查找表 (${txInfo.addressLookupTables.length}个):`);
            txInfo.addressLookupTables.forEach((table, index) => {
                console.log(`  ${index + 1}. ${table.tableAddress}`);
                if (table.resolvedAddresses && table.resolvedAddresses.length > 0) {
                    console.log(`     解析地址数量: ${table.resolvedAddresses.length}`);
                }
            });
        }
        // Mint信息
        if (txInfo.mints.length > 0) {
            console.log(`\n🪙 Mint地址 (${txInfo.mints.length}个):`);
            txInfo.mints.forEach((mint, index) => {
                console.log(`  ${index + 1}. ${mint.mint}`);
                if (mint.symbol)
                    console.log(`     代币: ${mint.symbol}`);
                if (mint.decimals !== undefined)
                    console.log(`     精度: ${mint.decimals}`);
                if (mint.amount !== undefined)
                    console.log(`     数量: ${mint.amount}`);
                // 显示该mint对应的市场池
                const mintPools = txInfo.marketPools.filter(pool => pool.tokenA === mint.mint || pool.tokenB === mint.mint);
                if (mintPools.length > 0) {
                    console.log(`     📊 相关市场池:`);
                    mintPools.forEach((pool, poolIndex) => {
                        console.log(`       ${poolIndex + 1}. ${pool.additionalInfo?.marketType || pool.dex} - ${pool.poolAddress}`);
                        if (pool.additionalInfo?.description) {
                            console.log(`          ${pool.additionalInfo.description}`);
                        }
                    });
                }
            });
        }
        // 市场池信息
        if (txInfo.marketPools.length > 0) {
            console.log(`\n🏊 市场池 (${txInfo.marketPools.length}个):`);
            txInfo.marketPools.forEach((pool, index) => {
                console.log(`  ${index + 1}. ${pool.poolAddress}`);
                console.log(`     DEX: ${pool.dex}`);
                console.log(`     类型: ${pool.poolType}`);
                if (pool.tokenA)
                    console.log(`     代币A: ${pool.tokenA}`);
                if (pool.tokenB)
                    console.log(`     代币B: ${pool.tokenB}`);
                if (pool.tvl)
                    console.log(`     TVL: $${pool.tvl.toLocaleString()}`);
            });
        }
        console.log('='.repeat(80));
    }
    /**
     * 保存交易到文件
     */
    async saveTransactionToFile(txInfo) {
        try {
            const date = new Date(txInfo.timestamp);
            const dateStr = date.toISOString().split('T')[0];
            const fileName = `transactions_${dateStr}.jsonl`;
            const filePath = path_1.default.join(this.outputDir, fileName);
            const logEntry = {
                timestamp: txInfo.timestamp,
                signature: txInfo.signature,
                slot: txInfo.slot,
                addressLookupTables: txInfo.addressLookupTables,
                mints: txInfo.mints,
                marketPools: txInfo.marketPools,
                accountKeys: txInfo.accountKeys,
                logMessages: txInfo.logMessages
            };
            // 追加到JSONL文件
            fs_1.default.appendFileSync(filePath, JSON.stringify(logEntry) + '\n');
            // 同时保存详细的JSON文件
            const detailFileName = `tx_${txInfo.signature}.json`;
            const detailFilePath = path_1.default.join(this.outputDir, 'details', detailFileName);
            // 确保details目录存在
            const detailDir = path_1.default.dirname(detailFilePath);
            if (!fs_1.default.existsSync(detailDir)) {
                fs_1.default.mkdirSync(detailDir, { recursive: true });
            }
            fs_1.default.writeFileSync(detailFilePath, JSON.stringify(txInfo, null, 2));
        }
        catch (error) {
            console.error("保存交易到文件失败:", error);
        }
    }
    /**
     * 确保输出目录存在
     */
    ensureOutputDirectory() {
        if (!fs_1.default.existsSync(this.outputDir)) {
            fs_1.default.mkdirSync(this.outputDir, { recursive: true });
            console.log(`创建输出目录: ${this.outputDir}`);
        }
    }
    /**
     * 获取提交级别
     */
    getCommitmentLevel(commitment) {
        switch (commitment) {
            case 'processed':
                return yellowstone_grpc_1.CommitmentLevel.PROCESSED;
            case 'confirmed':
                return yellowstone_grpc_1.CommitmentLevel.CONFIRMED;
            case 'finalized':
                return yellowstone_grpc_1.CommitmentLevel.FINALIZED;
            default:
                return yellowstone_grpc_1.CommitmentLevel.PROCESSED;
        }
    }
    /**
     * 保持连接活跃
     */
    keepAlive() {
        setInterval(() => {
            // 发送ping保持连接
            console.log(`[${new Date().toLocaleString()}] 监听中...`);
        }, 30000); // 每30秒输出一次状态
    }
    /**
     * 停止监听
     */
    async stop() {
        try {
            // @ts-ignore
            if (this.client && typeof this.client.close === 'function') {
                // @ts-ignore
                await this.client.close();
            }
            console.log("监听已停止");
        }
        catch (error) {
            console.error("停止监听时出错:", error);
        }
    }
}
exports.TransactionMonitor = TransactionMonitor;
//# sourceMappingURL=transaction-monitor.js.map