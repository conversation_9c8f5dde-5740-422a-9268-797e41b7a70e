import { MarketPoolInfo, TransactionInfo } from './types';
/**
 * 智能市场查询服务
 * 基于MEV交易模式分析，智能提取mint对应的真实市场地址
 */
export declare class PoolQueryService {
    private connection;
    private cache;
    private readonly PROGRAM_IDS;
    constructor(rpcUrl: string);
    /**
     * 查询mint对应的所有市场
     */
    queryPoolsByMint(mintAddress: string): Promise<MarketPoolInfo[]>;
    /**
     * 从MEV交易中智能提取mint-to-market映射
     * 基于精确的MEV交易模式分析
     */
    extractMintToMarketFromTransaction(transactionInfo: TransactionInfo): {
        [mint: string]: MarketPoolInfo[];
    };
    /**
     * 从MEV指令中提取市场地址（基于精确的位置规律）
     *
     * 规律：
     * - Pump.fun程序后第4位是market地址
     * - Meteora程序后第2位是market地址
     */
    private extractMarketsFromMevInstruction;
    /**
     * 从指令中提取市场地址（旧方法，保留作为备用）
     */
    private extractMarketsFromInstruction;
    /**
     * 判断是否为潜在的市场地址
     */
    private isPotentialMarketAddress;
    /**
     * 查询Pump.fun池子
     */
    private queryPumpPools;
    /**
     * 通过API查询Pump.fun市场
     */
    private queryPumpPoolsAPI;
    /**
     * 通过链上数据查询Pump.fun池子
     */
    private queryPumpPoolsOnChain;
    /**
     * 查询Raydium池子
     */
    private queryRaydiumPools;
    /**
     * 查询Meteora市场
     */
    private queryMeteoraPoolsAPI;
    /**
     * 查询Orca池子
     */
    private queryOrcaPools;
    /**
     * 通过DexScreener API查询池子（备用方法）
     */
    queryDexScreenerPools(mintAddress: string): Promise<MarketPoolInfo[]>;
    /**
     * 清除缓存
     */
    clearCache(): void;
}
//# sourceMappingURL=pool-query-service.d.ts.map