import { MarketPoolInfo } from './types';
/**
 * 池子查询服务
 * 通过各种API和链上数据查询mint对应的真实池子地址
 */
export declare class PoolQueryService {
    private connection;
    private cache;
    constructor(rpcUrl: string);
    /**
     * 查询mint对应的所有池子
     */
    queryPoolsByMint(mintAddress: string): Promise<MarketPoolInfo[]>;
    /**
     * 查询Pump.fun池子
     */
    private queryPumpPools;
    /**
     * 通过API查询Pump.fun池子
     */
    private queryPumpPoolsAPI;
    /**
     * 通过链上数据查询Pump.fun池子
     */
    private queryPumpPoolsOnChain;
    /**
     * 查询Raydium池子
     */
    private queryRaydiumPools;
    /**
     * 查询Meteora池子
     */
    private queryMeteoraPoolsAPI;
    /**
     * 查询Orca池子
     */
    private queryOrcaPools;
    /**
     * 通过DexScreener API查询池子（备用方法）
     */
    queryDexScreenerPools(mintAddress: string): Promise<MarketPoolInfo[]>;
    /**
     * 清除缓存
     */
    clearCache(): void;
}
//# sourceMappingURL=pool-query-service.d.ts.map