import { MonitorOptions } from './types';
export declare class TransactionMonitor {
    private client;
    private parser;
    private outputDir;
    private enableFileOutput;
    private enableConsoleOutput;
    constructor(grpcUrl: string, rpcUrl: string, outputDir?: string, enableFileOutput?: boolean, enableConsoleOutput?: boolean, grpcToken?: string);
    /**
     * 开始监听指定地址的交易
     */
    startMonitoring(options: MonitorOptions): Promise<void>;
    /**
     * 处理接收到的交易
     */
    private handleTransaction;
    /**
     * 控制台输出交易信息
     */
    private logTransactionToConsole;
    /**
     * 保存交易到文件
     */
    private saveTransactionToFile;
    /**
     * 确保输出目录存在
     */
    private ensureOutputDirectory;
    /**
     * 获取提交级别
     */
    private getCommitmentLevel;
    /**
     * 保持连接活跃
     */
    private keepAlive;
    /**
     * 停止监听
     */
    stop(): Promise<void>;
}
//# sourceMappingURL=transaction-monitor.d.ts.map