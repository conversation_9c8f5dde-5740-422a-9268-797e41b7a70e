"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PoolIdentifier = void 0;
const web3_js_1 = require("@solana/web3.js");
const types_1 = require("./types");
class PoolIdentifier {
    constructor(rpcUrl) {
        this.poolCache = new Map();
        this.connection = new web3_js_1.Connection(rpcUrl);
    }
    /**
     * 识别交易中涉及的池子
     */
    async identifyPools(accountKeys, instructions, logMessages) {
        const pools = [];
        const seenPools = new Set();
        // 1. 通过程序ID识别
        for (const instruction of instructions) {
            const programId = accountKeys[instruction.programIdIndex];
            if (this.isKnownDexProgram(programId)) {
                const poolInfo = await this.identifyPoolByProgram(programId, accountKeys, instruction);
                if (poolInfo && !seenPools.has(poolInfo.poolAddress)) {
                    pools.push(poolInfo);
                    seenPools.add(poolInfo.poolAddress);
                }
            }
        }
        // 2. 通过日志消息识别
        const logPools = this.identifyPoolsFromLogs(logMessages, accountKeys);
        for (const pool of logPools) {
            if (!seenPools.has(pool.poolAddress)) {
                pools.push(pool);
                seenPools.add(pool.poolAddress);
            }
        }
        // 3. 通过账户地址查询已知池子
        for (const accountKey of accountKeys) {
            if (!seenPools.has(accountKey)) {
                const poolInfo = await this.queryPoolByAddress(accountKey);
                if (poolInfo) {
                    pools.push(poolInfo);
                    seenPools.add(poolInfo.poolAddress);
                }
            }
        }
        return pools;
    }
    /**
     * 检查是否为已知DEX程序
     */
    isKnownDexProgram(programId) {
        return Object.values(types_1.PROGRAM_IDS).includes(programId);
    }
    /**
     * 通过程序ID识别池子
     */
    async identifyPoolByProgram(programId, accountKeys, instruction) {
        try {
            switch (programId) {
                case types_1.PROGRAM_IDS.RAYDIUM_AMM:
                    return this.parseRaydiumAmmPool(accountKeys, instruction);
                case types_1.PROGRAM_IDS.RAYDIUM_CLMM:
                    return this.parseRaydiumClmmPool(accountKeys, instruction);
                case types_1.PROGRAM_IDS.RAYDIUM_CP:
                    return this.parseRaydiumCpPool(accountKeys, instruction);
                case types_1.PROGRAM_IDS.ORCA_WHIRLPOOL:
                    return this.parseOrcaWhirlpoolPool(accountKeys, instruction);
                case types_1.PROGRAM_IDS.METEORA_DLMM:
                    return this.parseMeteoraPool(accountKeys, instruction);
                case types_1.PROGRAM_IDS.PUMP:
                    return this.parsePumpPool(accountKeys, instruction);
                case types_1.PROGRAM_IDS.JUPITER:
                    return this.parseJupiterPool(accountKeys, instruction);
                default:
                    return null;
            }
        }
        catch (error) {
            console.warn(`解析程序 ${programId} 的池子失败:`, error);
            return null;
        }
    }
    /**
     * 解析Raydium AMM池子
     */
    async parseRaydiumAmmPool(accountKeys, instruction) {
        // Raydium AMM池子通常在指令的第一个账户
        const poolAddress = accountKeys[instruction.accounts[0]];
        return {
            poolAddress,
            dex: types_1.DexType.RAYDIUM,
            tokenA: '', // 需要进一步查询
            tokenB: '',
            poolType: types_1.PoolType.AMM
        };
    }
    /**
     * 解析Raydium CLMM池子
     */
    async parseRaydiumClmmPool(accountKeys, instruction) {
        const poolAddress = accountKeys[instruction.accounts[0]];
        return {
            poolAddress,
            dex: types_1.DexType.RAYDIUM,
            tokenA: '',
            tokenB: '',
            poolType: types_1.PoolType.CLMM
        };
    }
    /**
     * 解析Raydium CP池子
     */
    async parseRaydiumCpPool(accountKeys, instruction) {
        const poolAddress = accountKeys[instruction.accounts[0]];
        return {
            poolAddress,
            dex: types_1.DexType.RAYDIUM,
            tokenA: '',
            tokenB: '',
            poolType: types_1.PoolType.AMM
        };
    }
    /**
     * 解析Orca Whirlpool池子
     */
    async parseOrcaWhirlpoolPool(accountKeys, instruction) {
        const poolAddress = accountKeys[instruction.accounts[0]];
        return {
            poolAddress,
            dex: types_1.DexType.ORCA,
            tokenA: '',
            tokenB: '',
            poolType: types_1.PoolType.CLMM
        };
    }
    /**
     * 解析Meteora池子
     */
    async parseMeteoraPool(accountKeys, instruction) {
        const poolAddress = accountKeys[instruction.accounts[0]];
        return {
            poolAddress,
            dex: types_1.DexType.METEORA,
            tokenA: '',
            tokenB: '',
            poolType: types_1.PoolType.DLMM
        };
    }
    /**
     * 解析Pump池子
     */
    async parsePumpPool(accountKeys, instruction) {
        const poolAddress = accountKeys[instruction.accounts[0]];
        return {
            poolAddress,
            dex: types_1.DexType.PUMP,
            tokenA: '',
            tokenB: '',
            poolType: types_1.PoolType.AMM
        };
    }
    /**
     * 解析Jupiter池子
     */
    async parseJupiterPool(accountKeys, instruction) {
        // Jupiter是聚合器，可能涉及多个池子
        return null;
    }
    /**
     * 从日志消息识别池子
     */
    identifyPoolsFromLogs(logMessages, accountKeys) {
        const pools = [];
        for (const log of logMessages) {
            // 检查Pump.fun相关日志
            if (log.includes('Program log: Instruction: InitializeMint2')) {
                // 这可能是新的Pump池子创建
                pools.push({
                    poolAddress: accountKeys[0] || '',
                    dex: types_1.DexType.PUMP,
                    tokenA: '',
                    tokenB: '',
                    poolType: types_1.PoolType.AMM
                });
            }
            // 检查其他DEX的特征日志
            if (log.includes('Raydium')) {
                // Raydium相关操作
            }
            if (log.includes('Orca') || log.includes('Whirlpool')) {
                // Orca相关操作
            }
        }
        return pools;
    }
    /**
     * 通过地址查询已知池子
     */
    async queryPoolByAddress(address) {
        // 检查缓存
        if (this.poolCache.has(address)) {
            return this.poolCache.get(address);
        }
        try {
            // 这里可以调用各种API来查询池子信息
            // 例如Shyft API、Raydium API等
            const poolInfo = await this.queryFromExternalAPIs(address);
            if (poolInfo) {
                this.poolCache.set(address, poolInfo);
                return poolInfo;
            }
        }
        catch (error) {
            console.warn(`查询地址 ${address} 的池子信息失败:`, error);
        }
        return null;
    }
    /**
     * 从外部API查询池子信息
     */
    async queryFromExternalAPIs(address) {
        // 这里可以实现对各种API的查询
        // 例如：Shyft、Raydium、Meteora等
        return null;
    }
}
exports.PoolIdentifier = PoolIdentifier;
//# sourceMappingURL=pool-identifier.js.map