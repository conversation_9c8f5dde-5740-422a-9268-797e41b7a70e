#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = exports.PoolIdentifier = exports.TransactionParser = exports.TransactionMonitor = void 0;
const transaction_monitor_1 = require("./transaction-monitor");
const config_1 = require("./config");
async function main() {
    try {
        console.log('🚀 Solana交易监听器启动中...');
        // 验证配置
        (0, config_1.validateConfig)();
        // 创建监听器
        const monitor = new transaction_monitor_1.TransactionMonitor(config_1.config.yellowstoneGrpcUrl, config_1.config.solanaRpcUrl, config_1.config.outputDir, config_1.config.enableFileOutput, config_1.config.enableConsoleOutput, config_1.config.yellowstoneGrpcToken);
        // 监听选项
        const options = {
            targetAddress: config_1.config.targetAddress,
            includeFailedTx: false,
            includeVoteTx: false,
            commitment: 'processed'
        };
        console.log('\n📊 监听配置:');
        console.log(`  目标地址: ${options.targetAddress}`);
        console.log(`  包含失败交易: ${options.includeFailedTx}`);
        console.log(`  包含投票交易: ${options.includeVoteTx}`);
        console.log(`  提交级别: ${options.commitment}`);
        console.log(`  文件输出: ${config_1.config.enableFileOutput ? '启用' : '禁用'}`);
        console.log(`  控制台输出: ${config_1.config.enableConsoleOutput ? '启用' : '禁用'}`);
        if (config_1.config.enableFileOutput) {
            console.log(`  输出目录: ${config_1.config.outputDir}`);
        }
        // 开始监听
        await monitor.startMonitoring(options);
        // 处理程序退出
        process.on('SIGINT', async () => {
            console.log('\n\n🛑 接收到退出信号，正在停止监听...');
            await monitor.stop();
            process.exit(0);
        });
        process.on('SIGTERM', async () => {
            console.log('\n\n🛑 接收到终止信号，正在停止监听...');
            await monitor.stop();
            process.exit(0);
        });
    }
    catch (error) {
        console.error('❌ 启动失败:', error);
        process.exit(1);
    }
}
// 如果直接运行此文件，则执行main函数
if (require.main === module) {
    main().catch(console.error);
}
var transaction_monitor_2 = require("./transaction-monitor");
Object.defineProperty(exports, "TransactionMonitor", { enumerable: true, get: function () { return transaction_monitor_2.TransactionMonitor; } });
var transaction_parser_1 = require("./transaction-parser");
Object.defineProperty(exports, "TransactionParser", { enumerable: true, get: function () { return transaction_parser_1.TransactionParser; } });
var pool_identifier_1 = require("./pool-identifier");
Object.defineProperty(exports, "PoolIdentifier", { enumerable: true, get: function () { return pool_identifier_1.PoolIdentifier; } });
__exportStar(require("./types"), exports);
var config_2 = require("./config");
Object.defineProperty(exports, "config", { enumerable: true, get: function () { return config_2.config; } });
//# sourceMappingURL=index.js.map