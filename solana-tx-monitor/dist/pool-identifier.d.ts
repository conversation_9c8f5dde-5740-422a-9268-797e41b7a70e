import { MarketPoolInfo } from './types';
export declare class PoolIdentifier {
    private connection;
    private poolCache;
    private mintToPoolCache;
    private poolQueryService;
    constructor(rpcUrl: string);
    /**
     * 识别交易中涉及的池子
     */
    identifyPools(accountKeys: string[], instructions: any[], logMessages: string[]): Promise<MarketPoolInfo[]>;
    /**
     * 检查是否为已知DEX程序
     */
    private isKnownDexProgram;
    /**
     * 通过程序ID识别池子
     */
    private identifyPoolByProgram;
    /**
     * 解析Raydium AMM池子
     */
    private parseRaydiumAmmPool;
    /**
     * 解析Raydium CLMM池子
     */
    private parseRaydiumClmmPool;
    /**
     * 解析Raydium CP池子
     */
    private parseRaydiumCpPool;
    /**
     * 解析Orca Whirlpool池子
     */
    private parseOrcaWhirlpoolPool;
    /**
     * 解析Meteora池子
     */
    private parseMeteoraPool;
    /**
     * 解析Pump池子
     */
    private parsePumpPool;
    /**
     * 解析Jupiter池子
     */
    private parseJupiterPool;
    /**
     * 从日志消息识别池子
     */
    private identifyPoolsFromLogs;
    /**
     * 通过地址查询已知池子
     */
    private queryPoolByAddress;
    /**
     * 从外部API查询池子信息
     */
    private queryFromExternalAPIs;
    /**
     * 根据mint地址查找对应的市场池
     */
    findPoolsByMint(mintAddress: string): Promise<MarketPoolInfo[]>;
    /**
     * 查找Pump.fun池子
     */
    private findPumpPoolsByMint;
    /**
     * 查找Meteora池子
     */
    private findMeteoraPoolsByMint;
    /**
     * 查找Raydium池子
     */
    private findRaydiumPoolsByMint;
    /**
     * 查找Orca池子
     */
    private findOrcaPoolsByMint;
    /**
     * 获取代币符号（简化版）
     */
    private getTokenSymbol;
}
//# sourceMappingURL=pool-identifier.d.ts.map