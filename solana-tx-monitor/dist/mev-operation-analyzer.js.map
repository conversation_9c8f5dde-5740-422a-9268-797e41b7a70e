{"version": 3, "file": "mev-operation-analyzer.js", "sourceRoot": "", "sources": ["../src/mev-operation-analyzer.ts"], "names": [], "mappings": ";;;AAAA,6CAA6C;AAG7C;;;GAGG;AACH,MAAa,oBAAoB;IAG/B,YAAY,MAAc;QACxB,IAAI,CAAC,UAAU,GAAG,IAAI,oBAAU,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,eAAgC;QAClD,MAAM,MAAM,GAAuB;YACjC,WAAW,EAAE,6CAA6C;YAC1D,UAAU,EAAE,EAAE;YACd,OAAO,EAAE;gBACP,eAAe,EAAE,CAAC;gBAClB,WAAW,EAAE,CAAC;gBACd,YAAY,EAAE,EAAE;gBAChB,UAAU,EAAE,CAAC;aACd;SACF,CAAC;QAEF,aAAa;QACb,MAAM,UAAU,GAAG,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QACnF,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;QAE/B,OAAO;QACP,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QAElD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,6BAA6B,CAAC,WAAqB;QACzD,MAAM,UAAU,GAAmB,EAAE,CAAC;QACtC,IAAI,gBAAgB,GAAiC,IAAI,CAAC;QAE1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAE/B,WAAW;YACX,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC;gBAClC,SAAS;gBACT,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,IAAI,EAAE,CAAC;oBAC9C,UAAU,CAAC,IAAI,CAAC,gBAAgC,CAAC,CAAC;gBACpD,CAAC;gBAED,gBAAgB,GAAG;oBACjB,IAAI,EAAE,OAAO;oBACb,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;oBACnC,OAAO,EAAE,EAAE;oBACX,KAAK,EAAE,EAAE;iBACV,CAAC;YACJ,CAAC;YAED,iBAAiB;YACjB,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;gBAC/E,IAAI,gBAAgB,EAAE,CAAC;oBACrB,MAAM,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;oBAC7D,IAAI,UAAU,EAAE,CAAC;wBACf,gBAAgB,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,IAAI,EAAE,CAAC;wBAC1D,gBAAgB,CAAC,KAAK,GAAG,gBAAgB,CAAC,KAAK,IAAI,EAAE,CAAC;wBACtD,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;wBACjD,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;oBACnD,CAAC;gBACH,CAAC;YACH,CAAC;YAED,gBAAgB;YAChB,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;gBAClF,IAAI,gBAAgB,EAAE,CAAC;oBACrB,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;oBAChE,IAAI,aAAa,EAAE,CAAC;wBAClB,gBAAgB,CAAC,OAAO,GAAG,gBAAgB,CAAC,OAAO,IAAI,EAAE,CAAC;wBAC1D,gBAAgB,CAAC,KAAK,GAAG,gBAAgB,CAAC,KAAK,IAAI,EAAE,CAAC;wBACtD,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;wBACpD,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;oBACtD,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,WAAW;QACX,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,IAAI,EAAE,CAAC;YAC9C,UAAU,CAAC,IAAI,CAAC,gBAAgC,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,OAAe;QACrC,eAAe;QACf,MAAM,aAAa,GAAG;YACpB,6CAA6C,EAAE,MAAM;YACrD,6CAA6C,EAAE,gBAAgB;YAC/D,8CAA8C,EAAE,2BAA2B;YAC3E,kCAAkC,EAAE,iBAAiB;YACrD,6CAA6C,CAAC,eAAe;SAC9D,CAAC;QAEF,IAAI,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACpC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,wBAAwB;QACxB,OAAO,OAAO,CAAC,MAAM,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC3H,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAe,EAAE,WAAqB,EAAE,KAAa;QAC5E,uBAAuB;QACvB,MAAM,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;QACnG,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CACxB,GAAG,CAAC,QAAQ,CAAC,6CAA6C,CAAC,IAAI,aAAa;YAC5E,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CACrB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,OAAe,EAAE,WAAqB,EAAE,KAAa;QAC5E,MAAM,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;QACnG,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CACxB,GAAG,CAAC,QAAQ,CAAC,6CAA6C,CAAC,IAAI,YAAY;YAC3E,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,CACxB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,WAAqB,EAAE,UAAkB;QACpE,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,YAAY;QACZ,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,UAAU,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACjG,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAE/B,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/B,MAAM,GAAG,OAAO,CAAC;YACnB,CAAC;iBAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,WAAqB,EAAE,UAAkB;QACpE,MAAM,KAAK,GAAa,EAAE,CAAC;QAC3B,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,EAAE,UAAU,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACjG,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAE/B,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/B,MAAM,GAAG,OAAO,CAAC;YACnB,CAAC;iBAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,IAAY;QAChC,WAAW;QACX,MAAM,UAAU,GAA8B;YAC5C,6CAA6C,EAAE,KAAK;YACpD,8CAA8C,EAAE,MAAM;YACtD,cAAc;YACd,8CAA8C,EAAE,IAAI,EAAE,QAAQ;YAC9D,8CAA8C,EAAE,KAAK,CAAC,QAAQ;SAC/D,CAAC;QAEF,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,UAA0B;QAChD,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3D,MAAM,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;QACvC,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YACtB,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAC1B,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAAE,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBAC1D,IAAI,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;oBAAE,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;YACH,UAAU,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,eAAe,EAAE,UAAU,CAAC,MAAM;YAClC,WAAW,EAAE,WAAW,CAAC,IAAI;YAC7B,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC;YACtC,UAAU;SACX,CAAC;IACJ,CAAC;CACF;AAxND,oDAwNC"}