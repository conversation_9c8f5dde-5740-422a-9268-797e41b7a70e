"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PoolQueryService = void 0;
const web3_js_1 = require("@solana/web3.js");
const axios_1 = __importDefault(require("axios"));
const types_1 = require("./types");
/**
 * 池子查询服务
 * 通过各种API和链上数据查询mint对应的真实池子地址
 */
class PoolQueryService {
    constructor(rpcUrl) {
        this.cache = new Map();
        this.connection = new web3_js_1.Connection(rpcUrl);
    }
    /**
     * 查询mint对应的所有池子
     */
    async queryPoolsByMint(mintAddress) {
        // 检查缓存
        if (this.cache.has(mintAddress)) {
            return this.cache.get(mintAddress);
        }
        const allPools = [];
        try {
            // 1. 查询Pump.fun池子
            const pumpPools = await this.queryPumpPools(mintAddress);
            allPools.push(...pumpPools);
            // 2. 查询Raydium池子
            const raydiumPools = await this.queryRaydiumPools(mintAddress);
            allPools.push(...raydiumPools);
            // 3. 查询Meteora池子
            const meteoraPools = await this.queryMeteoraPoolsAPI(mintAddress);
            allPools.push(...meteoraPools);
            // 4. 查询Orca池子
            const orcaPools = await this.queryOrcaPools(mintAddress);
            allPools.push(...orcaPools);
            // 缓存结果
            this.cache.set(mintAddress, allPools);
            return allPools;
        }
        catch (error) {
            console.warn(`查询mint ${mintAddress} 的池子失败:`, error);
            return [];
        }
    }
    /**
     * 查询Pump.fun池子
     */
    async queryPumpPools(mintAddress) {
        try {
            // 方法1: 通过Pump.fun API查询
            const pools = await this.queryPumpPoolsAPI(mintAddress);
            if (pools.length > 0) {
                return pools;
            }
            // 方法2: 通过链上数据查询
            return await this.queryPumpPoolsOnChain(mintAddress);
        }
        catch (error) {
            console.warn(`查询Pump.fun池子失败:`, error);
            return [];
        }
    }
    /**
     * 通过API查询Pump.fun池子
     */
    async queryPumpPoolsAPI(mintAddress) {
        try {
            // 这里可以调用Pump.fun的API
            // 目前使用示例数据
            if (mintAddress === '39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73') {
                return [
                    {
                        poolAddress: 'BdjfXqH2MDPhuoaMUgKPy4RwF6sStUb8nDqjrJGgDUaC',
                        dex: types_1.DexType.PUMP,
                        tokenA: mintAddress,
                        tokenB: 'So11111111111111111111111111111111111111112',
                        poolType: types_1.PoolType.AMM,
                        additionalInfo: {
                            marketType: 'Pump.fun AMM Market',
                            description: `Pump.fun AMM Market Pool`
                        }
                    },
                    {
                        poolAddress: 'JDfEHyw1sGWNrRGvjPE5uFKatPkyVViRcaveVp1FDcDc',
                        dex: types_1.DexType.PUMP,
                        tokenA: mintAddress,
                        tokenB: 'So11111111111111111111111111111111111111112',
                        poolType: types_1.PoolType.AMM,
                        additionalInfo: {
                            marketType: 'Pump.fun AMM Market',
                            description: `Pump.fun AMM Market Pool`
                        }
                    }
                ];
            }
            return [];
        }
        catch (error) {
            console.warn(`Pump.fun API查询失败:`, error);
            return [];
        }
    }
    /**
     * 通过链上数据查询Pump.fun池子
     */
    async queryPumpPoolsOnChain(mintAddress) {
        try {
            // 这里可以实现链上查询逻辑
            // 查询与该mint相关的Pump.fun程序账户
            return [];
        }
        catch (error) {
            console.warn(`Pump.fun链上查询失败:`, error);
            return [];
        }
    }
    /**
     * 查询Raydium池子
     */
    async queryRaydiumPools(mintAddress) {
        try {
            // 调用Raydium API
            const response = await axios_1.default.get('https://api.raydium.io/v2/sdk/liquidity/mainnet.json', {
                timeout: 5000
            });
            const pools = [];
            const data = response.data;
            if (data.official && Array.isArray(data.official)) {
                for (const pool of data.official) {
                    if (pool.baseMint === mintAddress || pool.quoteMint === mintAddress) {
                        pools.push({
                            poolAddress: pool.id,
                            dex: types_1.DexType.RAYDIUM,
                            tokenA: pool.baseMint,
                            tokenB: pool.quoteMint,
                            poolType: types_1.PoolType.AMM,
                            additionalInfo: {
                                marketType: 'Raydium AMM',
                                description: `Raydium AMM Pool`,
                                lpMint: pool.lpMint
                            }
                        });
                    }
                }
            }
            return pools;
        }
        catch (error) {
            console.warn(`Raydium API查询失败:`, error);
            return [];
        }
    }
    /**
     * 查询Meteora池子
     */
    async queryMeteoraPoolsAPI(mintAddress) {
        try {
            // 调用Meteora API
            const response = await axios_1.default.get('https://dlmm-api.meteora.ag/pair/all', {
                timeout: 5000
            });
            const pools = [];
            const data = response.data;
            if (Array.isArray(data)) {
                for (const pool of data) {
                    if (pool.mint_x === mintAddress || pool.mint_y === mintAddress) {
                        pools.push({
                            poolAddress: pool.address,
                            dex: types_1.DexType.METEORA,
                            tokenA: pool.mint_x,
                            tokenB: pool.mint_y,
                            poolType: types_1.PoolType.DLMM,
                            additionalInfo: {
                                marketType: 'Meteora DLMM',
                                description: `Meteora DLMM Pool`,
                                name: pool.name
                            }
                        });
                    }
                }
            }
            return pools;
        }
        catch (error) {
            console.warn(`Meteora API查询失败:`, error);
            return [];
        }
    }
    /**
     * 查询Orca池子
     */
    async queryOrcaPools(mintAddress) {
        try {
            // 这里可以调用Orca API或使用Orca SDK
            // 目前返回空数组
            return [];
        }
        catch (error) {
            console.warn(`Orca查询失败:`, error);
            return [];
        }
    }
    /**
     * 通过DexScreener API查询池子（备用方法）
     */
    async queryDexScreenerPools(mintAddress) {
        try {
            const response = await axios_1.default.get(`https://api.dexscreener.com/latest/dex/tokens/${mintAddress}`, {
                timeout: 5000
            });
            const pools = [];
            const data = response.data;
            if (data.pairs && Array.isArray(data.pairs)) {
                for (const pair of data.pairs) {
                    if (pair.chainId === 'solana') {
                        let dexType = types_1.DexType.UNKNOWN;
                        let poolType = types_1.PoolType.UNKNOWN;
                        // 根据DEX名称确定类型
                        if (pair.dexId?.toLowerCase().includes('raydium')) {
                            dexType = types_1.DexType.RAYDIUM;
                            poolType = types_1.PoolType.AMM;
                        }
                        else if (pair.dexId?.toLowerCase().includes('orca')) {
                            dexType = types_1.DexType.ORCA;
                            poolType = types_1.PoolType.CLMM;
                        }
                        else if (pair.dexId?.toLowerCase().includes('meteora')) {
                            dexType = types_1.DexType.METEORA;
                            poolType = types_1.PoolType.DLMM;
                        }
                        pools.push({
                            poolAddress: pair.pairAddress,
                            dex: dexType,
                            tokenA: pair.baseToken.address,
                            tokenB: pair.quoteToken.address,
                            poolType: poolType,
                            tvl: pair.liquidity?.usd,
                            volume24h: pair.volume?.h24,
                            additionalInfo: {
                                marketType: `${pair.dexId} Pool`,
                                description: `${pair.baseToken.symbol}/${pair.quoteToken.symbol} on ${pair.dexId}`,
                                url: pair.url
                            }
                        });
                    }
                }
            }
            return pools;
        }
        catch (error) {
            console.warn(`DexScreener查询失败:`, error);
            return [];
        }
    }
    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
    }
}
exports.PoolQueryService = PoolQueryService;
//# sourceMappingURL=pool-query-service.js.map