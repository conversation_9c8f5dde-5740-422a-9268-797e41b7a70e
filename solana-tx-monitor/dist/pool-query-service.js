"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PoolQueryService = void 0;
const web3_js_1 = require("@solana/web3.js");
const axios_1 = __importDefault(require("axios"));
const types_1 = require("./types");
/**
 * 智能市场查询服务
 * 基于MEV交易模式分析，智能提取mint对应的真实市场地址
 */
class PoolQueryService {
    constructor(rpcUrl) {
        this.cache = new Map();
        // 已知程序ID
        this.PROGRAM_IDS = {
            PUMP_NEW: 'pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA',
            METEORA: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
            TOKEN_PROGRAM: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
            SYSTEM_PROGRAM: '11111111111111111111111111111111',
            MEV_CONTRACT: 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz'
        };
        this.connection = new web3_js_1.Connection(rpcUrl);
    }
    /**
     * 查询mint对应的所有市场
     */
    async queryPoolsByMint(mintAddress) {
        // 检查缓存
        if (this.cache.has(mintAddress)) {
            return this.cache.get(mintAddress);
        }
        const allMarkets = [];
        try {
            // 1. 查询Pump.fun市场
            const pumpMarkets = await this.queryPumpPools(mintAddress);
            allMarkets.push(...pumpMarkets);
            // 2. 查询Meteora市场
            const meteoraMarkets = await this.queryMeteoraPoolsAPI(mintAddress);
            allMarkets.push(...meteoraMarkets);
            // 3. 查询Raydium市场
            const raydiumMarkets = await this.queryRaydiumPools(mintAddress);
            allMarkets.push(...raydiumMarkets);
            // 4. 查询Orca市场
            const orcaMarkets = await this.queryOrcaPools(mintAddress);
            allMarkets.push(...orcaMarkets);
            // 缓存结果
            this.cache.set(mintAddress, allMarkets);
            return allMarkets;
        }
        catch (error) {
            console.warn(`查询mint ${mintAddress} 的市场失败:`, error);
            return [];
        }
    }
    /**
     * 从MEV交易中智能提取mint-to-market映射
     * 基于精确的MEV交易模式分析
     */
    extractMintToMarketFromTransaction(transactionInfo) {
        const result = {};
        // 从交易中提取的mint
        const mints = transactionInfo.mints.map(m => m.mint);
        // 初始化结果
        mints.forEach(mint => {
            result[mint] = [];
        });
        // 分析每个指令
        transactionInfo.instructions.forEach((instruction, index) => {
            try {
                // 检查是否是MEV合约指令
                if (instruction.programId === this.PROGRAM_IDS.MEV_CONTRACT) {
                    console.log(`🎯 发现MEV合约指令 ${index + 1}，开始精确提取...`);
                    // 使用精确的MEV模式提取
                    const markets = this.extractMarketsFromMevInstruction(instruction, instruction.accounts);
                    console.log(`✅ 从MEV指令提取到 ${markets.length} 个市场`);
                    markets.forEach(market => {
                        console.log(`   ${market.additionalInfo?.dexType}: ${market.poolAddress} (${market.additionalInfo?.extractionMethod})`);
                    });
                    // 将市场分配给所有mint
                    mints.forEach(mint => {
                        result[mint].push(...markets);
                    });
                    return; // MEV指令处理完毕，跳过其他逻辑
                }
                // 处理其他类型的指令（备用逻辑）
                const programIndex = instruction.programIdIndex;
                if (programIndex !== undefined && transactionInfo.accountKeys[programIndex]) {
                    const programId = transactionInfo.accountKeys[programIndex];
                    // Pump.fun指令
                    if (programId === this.PROGRAM_IDS.PUMP_NEW) {
                        const markets = this.extractMarketsFromInstruction(instruction, transactionInfo.accountKeys, 'pump');
                        mints.forEach(mint => {
                            result[mint].push(...markets);
                        });
                    }
                    // Meteora指令
                    if (programId === this.PROGRAM_IDS.METEORA) {
                        const markets = this.extractMarketsFromInstruction(instruction, transactionInfo.accountKeys, 'meteora');
                        mints.forEach(mint => {
                            result[mint].push(...markets);
                        });
                    }
                }
            }
            catch (error) {
                console.warn(`分析指令 ${index} 失败:`, error);
            }
        });
        return result;
    }
    /**
     * 从MEV指令中提取市场地址（基于精确的位置规律）
     *
     * 规律：
     * - Pump.fun程序后第4位是market地址
     * - Meteora程序后第2位是market地址
     */
    extractMarketsFromMevInstruction(instruction, accountKeys) {
        const markets = [];
        if (!instruction.accounts || !Array.isArray(instruction.accounts)) {
            return markets;
        }
        const accounts = instruction.accounts;
        // 遍历账户列表，查找程序ID并提取对应的市场地址
        for (let i = 0; i < accounts.length; i++) {
            const account = accounts[i];
            const accountStr = account ? account.toString() : '';
            // 检查是否是Pump.fun程序
            if (accountStr === this.PROGRAM_IDS.PUMP_NEW) {
                // Pump.fun: 往下数第4位是market地址
                const marketIndex = i + 4;
                if (marketIndex < accounts.length) {
                    const marketAddress = accounts[marketIndex] ? accounts[marketIndex].toString() : '';
                    if (marketAddress) {
                        markets.push({
                            poolAddress: marketAddress,
                            dex: types_1.DexType.PUMP,
                            tokenA: 'EXTRACTED_FROM_TRANSACTION',
                            tokenB: 'So11111111111111111111111111111111111111112',
                            poolType: types_1.PoolType.AMM,
                            additionalInfo: {
                                marketType: 'Pump.fun AMM Market',
                                description: `Pump.fun AMM Market (位置 ${i}+4=${marketIndex})`,
                                extractedFromTransaction: true,
                                dexType: 'pump',
                                extractionMethod: 'MEV_PATTERN_PUMP_+4',
                                programPosition: i,
                                marketPosition: marketIndex
                            }
                        });
                    }
                }
            }
            // 检查是否是Meteora程序
            if (accountStr === this.PROGRAM_IDS.METEORA) {
                // Meteora: 往下数第2位是market地址
                const marketIndex = i + 2;
                if (marketIndex < accounts.length) {
                    const marketAddress = accounts[marketIndex] ? accounts[marketIndex].toString() : '';
                    if (marketAddress) {
                        markets.push({
                            poolAddress: marketAddress,
                            dex: types_1.DexType.METEORA,
                            tokenA: 'EXTRACTED_FROM_TRANSACTION',
                            tokenB: 'So11111111111111111111111111111111111111112',
                            poolType: types_1.PoolType.DLMM,
                            additionalInfo: {
                                marketType: 'Meteora DLMM Market',
                                description: `Meteora DLMM Market (位置 ${i}+2=${marketIndex})`,
                                extractedFromTransaction: true,
                                dexType: 'meteora',
                                extractionMethod: 'MEV_PATTERN_METEORA_+2',
                                programPosition: i,
                                marketPosition: marketIndex
                            }
                        });
                    }
                }
            }
        }
        return markets;
    }
    /**
     * 从指令中提取市场地址（旧方法，保留作为备用）
     */
    extractMarketsFromInstruction(instruction, accountKeys, dexType) {
        // 如果是MEV合约指令，使用精确的提取方法
        if (instruction.programId === this.PROGRAM_IDS.MEV_CONTRACT) {
            return this.extractMarketsFromMevInstruction(instruction, instruction.accounts);
        }
        // 否则使用原来的通用方法
        const markets = [];
        if (!instruction.accounts) {
            return markets;
        }
        const usedAccounts = instruction.accounts
            .map((accIndex) => accountKeys[accIndex])
            .filter((acc) => this.isPotentialMarketAddress(acc));
        usedAccounts.forEach((marketAddress) => {
            const dex = dexType === 'pump' ? types_1.DexType.PUMP : types_1.DexType.METEORA;
            const poolType = dexType === 'pump' ? types_1.PoolType.AMM : types_1.PoolType.DLMM;
            markets.push({
                poolAddress: marketAddress,
                dex: dex,
                tokenA: 'EXTRACTED_FROM_TRANSACTION',
                tokenB: 'So11111111111111111111111111111111111111112',
                poolType: poolType,
                additionalInfo: {
                    marketType: `${dexType === 'pump' ? 'Pump.fun' : 'Meteora'} Market`,
                    description: `${dexType === 'pump' ? 'Pump.fun AMM' : 'Meteora DLMM'} Market`,
                    extractedFromTransaction: true,
                    dexType: dexType,
                    extractionMethod: 'GENERIC_FILTER'
                }
            });
        });
        return markets;
    }
    /**
     * 判断是否为潜在的市场地址
     */
    isPotentialMarketAddress(address) {
        if (!address || typeof address !== 'string') {
            return false;
        }
        // 基本长度检查
        if (address.length < 32 || address.length > 44) {
            return false;
        }
        // 排除已知的程序地址
        if (Object.values(this.PROGRAM_IDS).includes(address)) {
            return false;
        }
        // 排除SOL mint
        if (address === 'So11111111111111111111111111111111111111112') {
            return false;
        }
        // 排除常见的系统账户模式
        if (address.startsWith('1111111') || address.endsWith('1111111')) {
            return false;
        }
        return true;
    }
    /**
     * 查询Pump.fun池子
     */
    async queryPumpPools(mintAddress) {
        try {
            // 方法1: 通过Pump.fun API查询
            const pools = await this.queryPumpPoolsAPI(mintAddress);
            if (pools.length > 0) {
                return pools;
            }
            // 方法2: 通过链上数据查询
            return await this.queryPumpPoolsOnChain(mintAddress);
        }
        catch (error) {
            console.warn(`查询Pump.fun池子失败:`, error);
            return [];
        }
    }
    /**
     * 通过API查询Pump.fun市场
     */
    async queryPumpPoolsAPI(mintAddress) {
        try {
            // 基于真实的MEV交易数据
            const knownMarkets = {
                // AP代币的市场
                '39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73': [
                    {
                        poolAddress: 'BdjfXqH2MDPhuoaMUgKPy4RwF6sStUb8nDqjrJGgDUaC', // 这是已知的池子地址
                        dex: types_1.DexType.PUMP,
                        tokenA: mintAddress,
                        tokenB: 'So11111111111111111111111111111111111111112',
                        poolType: types_1.PoolType.AMM,
                        additionalInfo: {
                            marketType: 'Pump.fun AMM Market',
                            description: `Pump.fun AMM (AP-WSOL) Market`,
                            marketAddress: 'NEED_TO_EXTRACT_FROM_TRANSACTION' // 需要从真实交易中提取
                        }
                    },
                    {
                        poolAddress: 'JDfEHyw1sGWNrRGvjPE5uFKatPkyVViRcaveVp1FDcDc', // 这是已知的池子地址
                        dex: types_1.DexType.PUMP,
                        tokenA: mintAddress,
                        tokenB: 'So11111111111111111111111111111111111111112',
                        poolType: types_1.PoolType.AMM,
                        additionalInfo: {
                            marketType: 'Pump.fun AMM Market',
                            description: `Pump.fun AMM (AP-WSOL) Market`,
                            marketAddress: 'NEED_TO_EXTRACT_FROM_TRANSACTION'
                        }
                    }
                ],
                // TAP代币的市场
                '5PBWkkui11zTUgAoEirQkrZybznoit21tfVHT2y25kgj': [
                    {
                        poolAddress: 'PUMP_TAP_MARKET_ADDRESS', // 需要从交易中提取
                        dex: types_1.DexType.PUMP,
                        tokenA: mintAddress,
                        tokenB: 'So11111111111111111111111111111111111111112',
                        poolType: types_1.PoolType.AMM,
                        additionalInfo: {
                            marketType: 'Pump.fun AMM Market',
                            description: `Pump.fun AMM (TAP-WSOL) Market`,
                            marketAddress: 'NEED_TO_EXTRACT_FROM_TRANSACTION'
                        }
                    }
                ]
            };
            return knownMarkets[mintAddress] || [];
        }
        catch (error) {
            console.warn(`Pump.fun API查询失败:`, error);
            return [];
        }
    }
    /**
     * 通过链上数据查询Pump.fun池子
     */
    async queryPumpPoolsOnChain(mintAddress) {
        try {
            // 这里可以实现链上查询逻辑
            // 查询与该mint相关的Pump.fun程序账户
            return [];
        }
        catch (error) {
            console.warn(`Pump.fun链上查询失败:`, error);
            return [];
        }
    }
    /**
     * 查询Raydium池子
     */
    async queryRaydiumPools(mintAddress) {
        try {
            // 调用Raydium API
            const response = await axios_1.default.get('https://api.raydium.io/v2/sdk/liquidity/mainnet.json', {
                timeout: 5000
            });
            const pools = [];
            const data = response.data;
            if (data.official && Array.isArray(data.official)) {
                for (const pool of data.official) {
                    if (pool.baseMint === mintAddress || pool.quoteMint === mintAddress) {
                        pools.push({
                            poolAddress: pool.id,
                            dex: types_1.DexType.RAYDIUM,
                            tokenA: pool.baseMint,
                            tokenB: pool.quoteMint,
                            poolType: types_1.PoolType.AMM,
                            additionalInfo: {
                                marketType: 'Raydium AMM',
                                description: `Raydium AMM Pool`,
                                lpMint: pool.lpMint
                            }
                        });
                    }
                }
            }
            return pools;
        }
        catch (error) {
            console.warn(`Raydium API查询失败:`, error);
            return [];
        }
    }
    /**
     * 查询Meteora市场
     */
    async queryMeteoraPoolsAPI(mintAddress) {
        try {
            // 首先检查已知的市场地址
            const knownMeteoraMarkets = {
                // TAP代币的Meteora市场 - 已确认的真实地址
                '5PBWkkui11zTUgAoEirQkrZybznoit21tfVHT2y25kgj': [
                    {
                        poolAddress: '22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH', // ✅ 真实的Market地址
                        dex: types_1.DexType.METEORA,
                        tokenA: mintAddress,
                        tokenB: 'So11111111111111111111111111111111111111112',
                        poolType: types_1.PoolType.DLMM,
                        additionalInfo: {
                            marketType: 'Meteora DLMM Market',
                            description: `Meteora (TAP-WSOL) Market`,
                            marketAddress: '22xJTnyPsK9zUrbr523bo8trX2cUi2eH7nY7Y2Ai1HoH',
                            isConfirmedMarket: true
                        }
                    }
                ],
                // AP代币的Meteora市场 - 需要从交易中提取
                '39zSVsSHFqNhARbVh6n8ZF78nCmhV3gSg8D39xhBNe73': [
                    {
                        poolAddress: 'METEORA_AP_MARKET_ADDRESS', // 需要从真实交易中提取
                        dex: types_1.DexType.METEORA,
                        tokenA: mintAddress,
                        tokenB: 'So11111111111111111111111111111111111111112',
                        poolType: types_1.PoolType.DLMM,
                        additionalInfo: {
                            marketType: 'Meteora DLMM Market',
                            description: `Meteora (AP-WSOL) Market`,
                            marketAddress: 'NEED_TO_EXTRACT_FROM_TRANSACTION'
                        }
                    }
                ]
            };
            // 如果有已知的市场，直接返回
            if (knownMeteoraMarkets[mintAddress]) {
                return knownMeteoraMarkets[mintAddress];
            }
            // 否则尝试API查询
            const response = await axios_1.default.get('https://dlmm-api.meteora.ag/pair/all', {
                timeout: 5000
            });
            const pools = [];
            const data = response.data;
            if (Array.isArray(data)) {
                for (const pool of data) {
                    if (pool.mint_x === mintAddress || pool.mint_y === mintAddress) {
                        pools.push({
                            poolAddress: pool.address,
                            dex: types_1.DexType.METEORA,
                            tokenA: pool.mint_x,
                            tokenB: pool.mint_y,
                            poolType: types_1.PoolType.DLMM,
                            additionalInfo: {
                                marketType: 'Meteora DLMM Market',
                                description: `Meteora DLMM Market`,
                                name: pool.name,
                                marketAddress: pool.address // API返回的地址可能就是市场地址
                            }
                        });
                    }
                }
            }
            return pools;
        }
        catch (error) {
            console.warn(`Meteora API查询失败:`, error);
            return [];
        }
    }
    /**
     * 查询Orca池子
     */
    async queryOrcaPools(mintAddress) {
        try {
            // 这里可以调用Orca API或使用Orca SDK
            // 目前返回空数组
            return [];
        }
        catch (error) {
            console.warn(`Orca查询失败:`, error);
            return [];
        }
    }
    /**
     * 通过DexScreener API查询池子（备用方法）
     */
    async queryDexScreenerPools(mintAddress) {
        try {
            const response = await axios_1.default.get(`https://api.dexscreener.com/latest/dex/tokens/${mintAddress}`, {
                timeout: 5000
            });
            const pools = [];
            const data = response.data;
            if (data.pairs && Array.isArray(data.pairs)) {
                for (const pair of data.pairs) {
                    if (pair.chainId === 'solana') {
                        let dexType = types_1.DexType.UNKNOWN;
                        let poolType = types_1.PoolType.UNKNOWN;
                        // 根据DEX名称确定类型
                        if (pair.dexId?.toLowerCase().includes('raydium')) {
                            dexType = types_1.DexType.RAYDIUM;
                            poolType = types_1.PoolType.AMM;
                        }
                        else if (pair.dexId?.toLowerCase().includes('orca')) {
                            dexType = types_1.DexType.ORCA;
                            poolType = types_1.PoolType.CLMM;
                        }
                        else if (pair.dexId?.toLowerCase().includes('meteora')) {
                            dexType = types_1.DexType.METEORA;
                            poolType = types_1.PoolType.DLMM;
                        }
                        pools.push({
                            poolAddress: pair.pairAddress,
                            dex: dexType,
                            tokenA: pair.baseToken.address,
                            tokenB: pair.quoteToken.address,
                            poolType: poolType,
                            tvl: pair.liquidity?.usd,
                            volume24h: pair.volume?.h24,
                            additionalInfo: {
                                marketType: `${pair.dexId} Pool`,
                                description: `${pair.baseToken.symbol}/${pair.quoteToken.symbol} on ${pair.dexId}`,
                                url: pair.url
                            }
                        });
                    }
                }
            }
            return pools;
        }
        catch (error) {
            console.warn(`DexScreener查询失败:`, error);
            return [];
        }
    }
    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
    }
}
exports.PoolQueryService = PoolQueryService;
//# sourceMappingURL=pool-query-service.js.map