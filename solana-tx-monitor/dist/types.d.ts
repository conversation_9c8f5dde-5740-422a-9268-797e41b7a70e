export interface TransactionInfo {
    signature: string;
    slot: number;
    timestamp: number;
    accountKeys: string[];
    instructions: any[];
    logMessages: string[];
    addressLookupTables: AddressLookupTableInfo[];
    mints: MintInfo[];
    marketPools: MarketPoolInfo[];
}
export interface AddressLookupTableInfo {
    tableAddress: string;
    writableIndexes: number[];
    readonlyIndexes: number[];
    resolvedAddresses?: string[];
}
export interface MintInfo {
    mint: string;
    decimals?: number;
    symbol?: string;
    name?: string;
    source: 'preTokenBalances' | 'postTokenBalances' | 'instruction';
    amount?: number;
    owner?: string;
}
export interface MarketPoolInfo {
    poolAddress: string;
    dex: DexType;
    tokenA: string;
    tokenB: string;
    poolType: PoolType;
    tvl?: number;
    volume24h?: number;
    fee?: number;
    additionalInfo?: any;
}
export declare enum DexType {
    RAYDIUM = "Raydium",
    ORCA = "Orca",
    METEORA = "Meteora",
    PUMP = "Pump",
    JUPITER = "Jupiter",
    WHIRLPOOL = "Whirlpool",
    UNKNOWN = "Unknown"
}
export declare enum PoolType {
    AMM = "AMM",
    CLMM = "CLMM",
    DLMM = "DLMM",
    STABLE = "Stable",
    UNKNOWN = "Unknown"
}
export declare const PROGRAM_IDS: {
    RAYDIUM_AMM: string;
    RAYDIUM_CLMM: string;
    RAYDIUM_CP: string;
    ORCA_WHIRLPOOL: string;
    METEORA_DLMM: string;
    PUMP_OLD: string;
    PUMP: string;
    JUPITER: string;
    TOKEN_PROGRAM: string;
    ASSOCIATED_TOKEN_PROGRAM: string;
    SYSTEM_PROGRAM: string;
};
export interface Config {
    yellowstoneGrpcUrl: string;
    yellowstoneGrpcToken?: string;
    solanaRpcUrl: string;
    targetAddress: string;
    outputDir: string;
    enableFileOutput: boolean;
    enableConsoleOutput: boolean;
    raydiumApiUrl: string;
    meteoraApiUrl: string;
    shyftApiKey?: string;
}
export interface MonitorOptions {
    targetAddress: string;
    includeFailedTx?: boolean;
    includeVoteTx?: boolean;
    commitment?: 'processed' | 'confirmed' | 'finalized';
}
export interface ParseResult {
    success: boolean;
    transactionInfo?: TransactionInfo;
    error?: string;
}
//# sourceMappingURL=types.d.ts.map