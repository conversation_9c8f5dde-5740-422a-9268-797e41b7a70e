import { TransactionInfo } from './types';
/**
 * MEV交易监听器
 *
 * 专门监听包含MEV合约的交易，并提取mint-to-market映射
 */
export declare class MevMonitor {
    private connection;
    private parser;
    private isMonitoring;
    private readonly MEV_CONTRACT;
    private onMevTransaction?;
    private onError?;
    constructor(rpcUrl: string);
    /**
     * 设置MEV交易回调
     */
    onMevTransactionDetected(callback: (txInfo: TransactionInfo, mapping: MintToMarketMapping) => void): void;
    /**
     * 设置错误回调
     */
    onErrorOccurred(callback: (error: Error) => void): void;
    /**
     * 开始监听MEV交易
     */
    startMonitoring(): Promise<void>;
    /**
     * 停止监听
     */
    stopMonitoring(): void;
    /**
     * 处理MEV交易
     */
    private handleMevTransaction;
    /**
     * 验证交易是否包含MEV合约
     */
    private verifyMevContract;
    /**
     * 提取mint-to-market映射
     */
    private extractMintToMarketMapping;
    /**
     * 显示MEV交易信息
     */
    private displayMevTransactionInfo;
    /**
     * 手动分析特定交易
     */
    analyzeTransaction(signature: string): Promise<MintToMarketMapping | null>;
}
export interface MintToMarketMapping {
    [mintAddress: string]: {
        mint: string;
        symbol: string;
        decimals?: number;
        markets: {
            pump: MarketInfo[];
            meteora: MarketInfo[];
        };
    };
}
export interface MarketInfo {
    address: string;
    method: string;
    programPosition?: number;
    marketPosition?: number;
}
//# sourceMappingURL=mev-monitor.d.ts.map