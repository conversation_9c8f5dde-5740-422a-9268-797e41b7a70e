"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionParser = void 0;
const bs58_1 = __importDefault(require("bs58"));
const web3_js_1 = require("@solana/web3.js");
const pool_identifier_1 = require("./pool-identifier");
class TransactionParser {
    constructor(rpcUrl) {
        this.connection = new web3_js_1.Connection(rpcUrl);
        this.poolIdentifier = new pool_identifier_1.PoolIdentifier(rpcUrl);
    }
    /**
     * 解析Yellowstone gRPC交易数据
     */
    async parseTransaction(data) {
        try {
            if (!data.transaction) {
                return { success: false, error: 'No transaction data' };
            }
            const txData = data.transaction.transaction;
            const metaData = data.transaction.transaction.meta;
            // 基础交易信息
            const signature = bs58_1.default.encode(txData.transaction.signatures[0]);
            const slot = data.transaction.slot;
            const timestamp = Date.now();
            // 解析账户密钥
            const accountKeys = txData.transaction.message.accountKeys.map((ak) => bs58_1.default.encode(ak));
            // 解析指令
            const instructions = txData.transaction.message.instructions;
            // 解析日志消息
            const logMessages = metaData?.logMessages || [];
            // 解析地址查找表
            const addressLookupTables = await this.parseAddressLookupTables(txData.transaction.message);
            // 解析mint信息
            const mints = this.parseMints(metaData);
            // 识别市场池
            const marketPools = await this.identifyMarketPools(accountKeys, instructions, logMessages);
            // 为每个mint查找对应的市场池
            const mintMarketPools = [];
            for (const mint of mints) {
                if (mint.mint !== 'So11111111111111111111111111111111111111112') { // 跳过SOL
                    const pools = await this.poolIdentifier.findPoolsByMint(mint.mint);
                    mintMarketPools.push(...pools);
                }
            }
            // 合并所有池子信息
            const allMarketPools = [...marketPools, ...mintMarketPools];
            const transactionInfo = {
                signature,
                slot,
                timestamp,
                accountKeys,
                instructions,
                logMessages,
                addressLookupTables,
                mints,
                marketPools: allMarketPools
            };
            return { success: true, transactionInfo };
        }
        catch (error) {
            return {
                success: false,
                error: `解析交易失败: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }
    /**
     * 解析地址查找表
     */
    async parseAddressLookupTables(message) {
        const lookupTables = [];
        if (message.addressTableLookups) {
            for (const lookup of message.addressTableLookups) {
                const tableInfo = {
                    tableAddress: bs58_1.default.encode(lookup.accountKey),
                    writableIndexes: lookup.writableIndexes || [],
                    readonlyIndexes: lookup.readonlyIndexes || []
                };
                // 尝试解析查找表中的地址
                try {
                    const resolvedAddresses = await this.resolveAddressLookupTable(tableInfo.tableAddress);
                    tableInfo.resolvedAddresses = resolvedAddresses;
                }
                catch (error) {
                    console.warn(`无法解析查找表 ${tableInfo.tableAddress}:`, error);
                }
                lookupTables.push(tableInfo);
            }
        }
        return lookupTables;
    }
    /**
     * 解析查找表中的地址
     */
    async resolveAddressLookupTable(tableAddress) {
        try {
            const pubkey = new web3_js_1.PublicKey(tableAddress);
            const accountInfo = await this.connection.getAccountInfo(pubkey);
            if (!accountInfo) {
                return [];
            }
            // 解析查找表数据结构
            const data = accountInfo.data;
            const LOOKUP_TABLE_META_SIZE = 56;
            if (data.length < LOOKUP_TABLE_META_SIZE) {
                return [];
            }
            const serializedAddressesLen = data.length - LOOKUP_TABLE_META_SIZE;
            const numAddresses = serializedAddressesLen / 32;
            const addresses = [];
            for (let i = 0; i < numAddresses; i++) {
                const start = LOOKUP_TABLE_META_SIZE + (i * 32);
                const addressBytes = data.slice(start, start + 32);
                addresses.push(bs58_1.default.encode(addressBytes));
            }
            return addresses;
        }
        catch (error) {
            console.warn(`解析查找表失败 ${tableAddress}:`, error);
            return [];
        }
    }
    /**
     * 解析mint信息
     */
    parseMints(metaData) {
        const mints = [];
        const seenMints = new Set();
        // 从preTokenBalances解析
        if (metaData?.preTokenBalances) {
            for (const balance of metaData.preTokenBalances) {
                if (balance.mint && !seenMints.has(balance.mint)) {
                    mints.push({
                        mint: balance.mint,
                        decimals: balance.uiTokenAmount?.decimals,
                        source: 'preTokenBalances',
                        amount: balance.uiTokenAmount?.uiAmount,
                        owner: balance.owner
                    });
                    seenMints.add(balance.mint);
                }
            }
        }
        // 从postTokenBalances解析
        if (metaData?.postTokenBalances) {
            for (const balance of metaData.postTokenBalances) {
                if (balance.mint && !seenMints.has(balance.mint)) {
                    mints.push({
                        mint: balance.mint,
                        decimals: balance.uiTokenAmount?.decimals,
                        source: 'postTokenBalances',
                        amount: balance.uiTokenAmount?.uiAmount,
                        owner: balance.owner
                    });
                    seenMints.add(balance.mint);
                }
            }
        }
        return mints;
    }
    /**
     * 识别市场池
     */
    async identifyMarketPools(accountKeys, instructions, logMessages) {
        return await this.poolIdentifier.identifyPools(accountKeys, instructions, logMessages);
    }
}
exports.TransactionParser = TransactionParser;
//# sourceMappingURL=transaction-parser.js.map