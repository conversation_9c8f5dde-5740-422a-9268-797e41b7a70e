import { TransactionInfo } from './types';
/**
 * MEV操作分析器
 * 专门分析MEV合约中的操作，提取mint和对应的market信息
 */
export declare class MevOperationAnalyzer {
    private connection;
    constructor(rpcUrl: string);
    /**
     * 分析MEV交易操作
     */
    analyzeMevOperation(transactionInfo: TransactionInfo): MevOperationResult;
    /**
     * 从账户密钥中提取操作信息
     */
    private extractOperationsFromAccounts;
    /**
     * 检测是否为潜在的mint地址
     */
    private isPotentialMint;
    /**
     * 检测是否为Pump.fun相关账户
     */
    private isPumpfunAccount;
    /**
     * 检测是否为Meteora相关账户
     */
    private isMeteoraAccount;
    /**
     * 提取Pump.fun市场信息
     */
    private extractPumpfunMarket;
    /**
     * 提取Meteora市场信息
     */
    private extractMeteoraMarket;
    /**
     * 获取mint符号
     */
    private getMintSymbol;
    /**
     * 生成操作摘要
     */
    private generateSummary;
}
export interface MevOperation {
    mint: string;
    symbol: string;
    markets: string[];
    pools: string[];
}
export interface MevOperationSummary {
    totalOperations: number;
    uniqueMints: number;
    involvedDEXs: string[];
    totalPools: number;
}
export interface MevOperationResult {
    mevContract: string;
    operations: MevOperation[];
    summary: MevOperationSummary;
}
//# sourceMappingURL=mev-operation-analyzer.d.ts.map