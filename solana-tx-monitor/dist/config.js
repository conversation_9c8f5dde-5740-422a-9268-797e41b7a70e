"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
exports.validateConfig = validateConfig;
const dotenv_1 = __importDefault(require("dotenv"));
// 加载环境变量
dotenv_1.default.config();
exports.config = {
    yellowstoneGrpcUrl: process.env.YELLOWSTONE_GRPC_URL || 'https://test-grpc.chainbuff.com',
    yellowstoneGrpcToken: process.env.YELLOWSTONE_GRPC_TOKEN,
    solanaRpcUrl: process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
    targetAddress: process.env.TARGET_ADDRESS || 'MEViEnscUm6tsQRoGd9h6nLQaQspKj7DB2M5FwM3Xvz',
    outputDir: process.env.OUTPUT_DIR || './data',
    enableFileOutput: process.env.ENABLE_FILE_OUTPUT === 'true',
    enableConsoleOutput: process.env.ENABLE_CONSOLE_OUTPUT !== 'false',
    raydiumApiUrl: process.env.RAYDIUM_API_URL || 'https://api.raydium.io/v2/sdk/liquidity/mainnet.json',
    meteoraApiUrl: process.env.METEORA_API_URL || 'https://dlmm-api.meteora.ag/pair/all',
    shyftApiKey: process.env.SHYFT_API_KEY
};
// 验证配置
function validateConfig() {
    if (!exports.config.targetAddress) {
        throw new Error('TARGET_ADDRESS is required');
    }
    if (!exports.config.yellowstoneGrpcUrl) {
        throw new Error('YELLOWSTONE_GRPC_URL is required');
    }
    if (!exports.config.solanaRpcUrl) {
        throw new Error('SOLANA_RPC_URL is required');
    }
    console.log('配置验证通过');
    console.log(`目标地址: ${exports.config.targetAddress}`);
    console.log(`Yellowstone gRPC: ${exports.config.yellowstoneGrpcUrl}`);
    console.log(`Solana RPC: ${exports.config.solanaRpcUrl}`);
}
//# sourceMappingURL=config.js.map