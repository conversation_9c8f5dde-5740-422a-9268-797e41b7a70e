import { ParseResult } from './types';
export declare class TransactionParser {
    private connection;
    private poolIdentifier;
    constructor(rpcUrl: string);
    /**
     * 解析Yellowstone gRPC交易数据
     */
    parseTransaction(data: any): Promise<ParseResult>;
    /**
     * 解析地址查找表
     */
    private parseAddressLookupTables;
    /**
     * 解析查找表中的地址
     */
    private resolveAddressLookupTable;
    /**
     * 解析mint信息
     */
    private parseMints;
    /**
     * 识别市场池
     */
    private identifyMarketPools;
}
//# sourceMappingURL=transaction-parser.d.ts.map