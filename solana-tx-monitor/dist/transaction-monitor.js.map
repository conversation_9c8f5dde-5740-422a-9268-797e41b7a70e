{"version": 3, "file": "transaction-monitor.js", "sourceRoot": "", "sources": ["../src/transaction-monitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iFAAyF;AACzF,6DAAyD;AAEzD,4CAAoB;AACpB,gDAAwB;AAExB,MAAa,kBAAkB;IAO7B,YACE,OAAe,EACf,MAAc,EACd,YAAoB,QAAQ,EAC5B,mBAA4B,IAAI,EAChC,sBAA+B,IAAI,EACnC,SAAkB;QAElB,YAAY;QACZ,MAAM,aAAa,GAAG;YACpB,iCAAiC,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ;SAC/D,CAAC;QAEF,IAAI,SAAS,EAAE,CAAC;YACd,aAAa;YACb,aAAa,CAAC,eAAe,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;QAC5D,CAAC;QAED,aAAa;QACb,IAAI,CAAC,MAAM,GAAG,IAAI,0BAAM,CAAC,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;QAC5D,IAAI,CAAC,MAAM,GAAG,IAAI,sCAAiB,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;QAE/C,WAAW;QACX,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,OAAuB;QAC3C,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,WAAW,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAE1B,UAAU;YACV,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAE7C,SAAS;YACT,MAAM,OAAO,GAAqB;gBAChC,QAAQ,EAAE,EAAE;gBACZ,KAAK,EAAE,EAAE;gBACT,YAAY,EAAE;oBACZ,GAAG,EAAE;wBACH,IAAI,EAAE,OAAO,CAAC,aAAa,IAAI,KAAK;wBACpC,MAAM,EAAE,OAAO,CAAC,eAAe,IAAI,KAAK;wBACxC,SAAS,EAAE,SAAS;wBACpB,cAAc,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC;wBACvC,cAAc,EAAE,EAAE;wBAClB,eAAe,EAAE,EAAE;qBACpB;iBACF;gBACD,kBAAkB,EAAE,EAAE;gBACtB,MAAM,EAAE,EAAE;gBACV,UAAU,EAAE,EAAE;gBACd,KAAK,EAAE,EAAE;gBACT,iBAAiB,EAAE,EAAE;gBACrB,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAU,IAAI,WAAW,CAAC;gBACtE,IAAI,EAAE,SAAS;aAChB,CAAC;YAEF,SAAS;YACT,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC5B,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;YAEjC,WAAW;YACX,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;gBAC/B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACrB,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,OAAO;YACP,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC3B,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;YAEH,SAAS;YACT,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;gBACpB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC;YAEH,OAAO;YACP,IAAI,CAAC,SAAS,EAAE,CAAC;QAEnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAChC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,IAAS;QACvC,IAAI,CAAC;YACH,OAAO;YACP,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YAE7D,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBACzB,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;gBAC5C,OAAO;YACT,CAAC;YAED,MAAM,eAAe,GAAG,WAAW,CAAC,eAAgB,CAAC;YAErD,QAAQ;YACR,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC7B,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,CAAC;YAChD,CAAC;YAED,OAAO;YACP,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,MAAM,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;YACpD,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,MAAuB;QACrD,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,UAAU,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,UAAU,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;QACrC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QACpE,OAAO,CAAC,GAAG,CAAC,qCAAqC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;QAErE,QAAQ;QACR,IAAI,MAAM,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAC,eAAe,MAAM,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,CAAC;YACnE,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBAClD,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;gBACrD,IAAI,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAClE,OAAO,CAAC,GAAG,CAAC,gBAAgB,KAAK,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,SAAS;QACT,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,OAAO,CAAC,GAAG,CAAC,gBAAgB,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;YACtD,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;gBACnC,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC5C,IAAI,IAAI,CAAC,MAAM;oBAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;gBACxD,IAAI,IAAI,CAAC,QAAQ,KAAK,SAAS;oBAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC1E,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS;oBAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;QACL,CAAC;QAED,QAAQ;QACR,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,aAAa,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC;YACzD,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;gBACzC,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;gBACnD,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACzC,IAAI,IAAI,CAAC,MAAM;oBAAE,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;gBACzD,IAAI,IAAI,CAAC,MAAM;oBAAE,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;gBACzD,IAAI,IAAI,CAAC,GAAG;oBAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;YACvE,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,MAAuB;QACzD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACxC,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,MAAM,QAAQ,GAAG,gBAAgB,OAAO,QAAQ,CAAC;YACjD,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAErD,MAAM,QAAQ,GAAG;gBACf,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,mBAAmB,EAAE,MAAM,CAAC,mBAAmB;gBAC/C,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,WAAW,EAAE,MAAM,CAAC,WAAW;aAChC,CAAC;YAEF,aAAa;YACb,YAAE,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;YAE7D,gBAAgB;YAChB,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,SAAS,OAAO,CAAC;YACrD,MAAM,cAAc,GAAG,cAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;YAE5E,gBAAgB;YAChB,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;YAC/C,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,YAAE,CAAC,SAAS,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAC/C,CAAC;YAED,YAAE,CAAC,aAAa,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QAEpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACnC,YAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAClD,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,UAAkB;QAC3C,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,WAAW;gBACd,OAAO,kCAAe,CAAC,SAAS,CAAC;YACnC,KAAK,WAAW;gBACd,OAAO,kCAAe,CAAC,SAAS,CAAC;YACnC,KAAK,WAAW;gBACd,OAAO,kCAAe,CAAC,SAAS,CAAC;YACnC;gBACE,OAAO,kCAAe,CAAC,SAAS,CAAC;QACrC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,SAAS;QACf,WAAW,CAAC,GAAG,EAAE;YACf,aAAa;YACb,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QACzD,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,aAAa;IAC1B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,IAAI,CAAC;YACH,aAAa;YACb,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,UAAU,EAAE,CAAC;gBAC3D,aAAa;gBACb,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAC5B,CAAC;YACD,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;CACF;AAjRD,gDAiRC"}