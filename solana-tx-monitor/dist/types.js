"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PROGRAM_IDS = exports.PoolType = exports.DexType = void 0;
// DEX类型
var DexType;
(function (DexType) {
    DexType["RAYDIUM"] = "Raydium";
    DexType["ORCA"] = "Orca";
    DexType["METEORA"] = "Meteora";
    DexType["PUMP"] = "Pump";
    DexType["JUPITER"] = "Jupiter";
    DexType["WHIRLPOOL"] = "Whirlpool";
    DexType["UNKNOWN"] = "Unknown";
})(DexType || (exports.DexType = DexType = {}));
// 池子类型
var PoolType;
(function (PoolType) {
    PoolType["AMM"] = "AMM";
    PoolType["CLMM"] = "CLMM";
    PoolType["DLMM"] = "DLMM";
    PoolType["STABLE"] = "Stable";
    PoolType["UNKNOWN"] = "Unknown";
})(PoolType || (exports.PoolType = PoolType = {}));
// 已知程序ID
exports.PROGRAM_IDS = {
    RAYDIUM_AMM: '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8',
    RAYDIUM_CLMM: 'CAMMCzo5YL8w4VFF8KVHrK22GGUQpMkFr9WeqATV9Uu',
    RAYDIUM_CP: 'CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C',
    ORCA_WHIRLPOOL: 'whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc',
    METEORA_DLMM: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
    PUMP: '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P',
    JUPITER: 'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4',
    TOKEN_PROGRAM: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
    ASSOCIATED_TOKEN_PROGRAM: 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL',
    SYSTEM_PROGRAM: '11111111111111111111111111111111'
};
//# sourceMappingURL=types.js.map