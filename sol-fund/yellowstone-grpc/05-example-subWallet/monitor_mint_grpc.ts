import Client, { CommitmentLevel, SubscribeRequest } from "@triton-one/yellowstone-grpc";
import bs58 from 'bs58';
import { Connection, PublicKey, LAMPORTS_PER_SOL } from "@solana/web3.js";
import { TokenBalance, TransactionData, TokenInfo } from "./types/transaction";
import { formatTransactionOutput } from "./utils/logger";
import sqlite3 from 'sqlite3';
import express from 'express';

// 创建数据库连接
const db = new sqlite3.Database('mints.db');

// 初始化数据库表
db.serialize(() => {
  // 删除旧表（如果存在）
  db.run(`DROP TABLE IF EXISTS mints`);
  
  // 创建新表
  db.run(`
    CREATE TABLE mints (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      mint_id TEXT NOT NULL UNIQUE,
      count INTEGER DEFAULT 1,
      first_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_seen DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
});

const black_list = ['So11111111111111111111111111111111111111112']; // 过滤wsol地址和其他的地址
const addresses = ['Arb1wtZQbbxEuC1D4WD4Lk2ix6xv9KetB7CHdiuiMc6B'] // 监控的钱包地址

const CONTRACTS = {
    PUMP: '6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P',
    RAYDIUM: '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8'
};

  /**
     * 检查代币余额变化
     * 分析交易前后的代币余额变化情况
     * @param data 交易数据
     * @returns 返回代币变化信息，包括代币地址和变化数量
     */
  async function checkTokenBalances(data: TransactionData) {
    const preTokenBalances = data.transaction.transaction.meta.preTokenBalances
    const postTokenBalances = data.transaction.transaction.meta.postTokenBalances
    // console.log(data.transaction.transaction.meta);
    
    console.log("交易前：：：：");
    if (preTokenBalances){
        for (const preBalance of preTokenBalances) {
            console.log("preBalance:" + JSON.stringify(preBalance, null, 2));
        }
    }
    console.log("交易后：：：：");
   if (postTokenBalances){
        for (const postBalance of postTokenBalances) {
            console.log("postBalance:" + JSON.stringify(postBalance, null, 2));
            
        }
    }
    

    const firstMint = getFirstMint(preTokenBalances);
    console.log("第一个有效的 mint 值:", firstMint);


    // 如果没有代币余额变化，返回 null
    if (postTokenBalances.length === 0) {
        console.log('没有代币余额变化');
        // return null
    }else{
         // 遍历所有代币余额变化
        for (const postBalance of postTokenBalances) {
            // 跳过不相关的地址
            if (!addresses.includes(postBalance.owner)) continue
            // 跳过 Wrapped SOL
            if (black_list.includes(postBalance.mint)) continue
            
            // 查找交易前的余额
            const preBalance = preTokenBalances.find(
                (pre: TokenBalance) => pre.owner === postBalance.owner && pre.mint === postBalance.mint
            )?.uiTokenAmount.uiAmount || 0
            
            
            console.log("交易前的余额:"+preBalance);
                    
            
            // 计算余额变化
            const change = postBalance.uiTokenAmount.uiAmount - preBalance;
            console.log("余额变化:"+change);
            
            if (change !== 0) {
                return {
                    mint: postBalance.mint,
                    amount: change
                };
            }
        }

    }



   

    return null;
}

// 存储或更新 mint 到数据库
async function storeMintToDb(mint: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const query = `
      INSERT INTO mints (mint_id, count, last_seen) 
      VALUES (?, 1, CURRENT_TIMESTAMP)
      ON CONFLICT(mint_id) DO UPDATE SET 
        count = count + 1,
        last_seen = CURRENT_TIMESTAMP
    `;
    
    db.run(query, [mint], (err) => {
      if (err) {
        console.error('Error storing/updating mint:', err);
        reject(err);
      } else {
        resolve();
      }
    });
  });
}

// 获取热门 mint
async function getTopMints(): Promise<any[]> {
  return new Promise((resolve, reject) => {
    const query = `
      SELECT 
        mint_id,
        count,
        first_seen,
        last_seen,
        (strftime('%s', last_seen) - strftime('%s', first_seen)) / 3600.0 as hours_active
      FROM mints 
      WHERE last_seen > datetime('now', '-24 hours')
      ORDER BY count DESC 
      LIMIT 10
    `;
    
    db.all(query, [], (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

// 取交易前的数组的第一个值
async function getFirstMint(preBalances) {
    for (const balance of preBalances) {
      if (balance.mint !== "So11111111111111111111111111111111111111112") {
        console.log("取得的mint:", balance.mint);
        // 存储到数据库
        await storeMintToDb(balance.mint);
        return balance.mint;
      }
    }
    return null; // 如果没有找到合适的 mint 值
}

 /**
     * 计算 SOL 余额变化
     * @param data 交易数据
     * @returns SOL 变化数量（正数表示增加，负数表示减少）
     */
function calculateSolChange(data: TransactionData): number {
    const preBalance = data.transaction.transaction.meta.preBalances[0];
    const postBalance = data.transaction.transaction.meta.postBalances[0];
    return (postBalance - preBalance) / LAMPORTS_PER_SOL;
}


/**
 * 检查交易中的余额变化
 * 分析 SOL 和代币的变化情况，确定是买入还是卖出操作
 * @param data 交易数据
 */
async  function checkBalances(data: TransactionData): Promise<TokenInfo | null> {
    const solChange = calculateSolChange(data);
    

    

    // 这里有问题
    const tokenInfo = await checkTokenBalances(data);
    console.log("tokeninfo:"+tokenInfo);
    
    if (!tokenInfo || black_list.includes(tokenInfo.mint)) {
        return null;
    }
    
    
    console.log('solAmount:'+ Math.abs(solChange));
    console.log('tokenAmount:'+ Math.abs(tokenInfo.amount));
    
    const output = formatTransactionOutput({
        slot: data.transaction.slot,
        wallet: addresses[0], 
        // contract: this.watchedContracts.includes('6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P') ? 'Pump' : 'Raydium',
        type: solChange < 0 ? '买入' : '卖出',
        tokenMint: tokenInfo.mint,
        solAmount: Math.abs(solChange),
        tokenAmount: Math.abs(tokenInfo.amount)
    });

    console.log(output);
    return tokenInfo;
}

async function main() {
    // 创建 Express 应用
    const app = express();
    const port = 3000;

    // API 路由
    app.get('/api/top-mints', async (req, res) => {
      try {
        const topMints = await getTopMints();
        res.json(topMints);
      } catch (error) {
        res.status(500).json({ error: 'Failed to fetch top mints' });
      }
    });

    // 启动 Express 服务器
    app.listen(port, () => {
      console.log(`Server is running on port ${port}`);
    });

    // 创建client
    // @ts-ignore
    const client = new Client.default(
        "https://test-grpc.chainbuff.com",
        undefined,
        {
            "grpc.max_receive_message_length": 128 * 1024 * 1024, // 128MB
        }
    );
    console.log("Subscribing to event stream...");

    // 创建订阅数据流
    const stream = await client.subscribe();

    // 创建订阅请求
    const request: SubscribeRequest = {
        accounts: {},
        slots: {},
        transactions: {
            txn: {
                vote: false,
                failed: false,
                signature: undefined,
                accountInclude: addresses, // 移除多余的空格
                accountExclude: [],
                accountRequired: [],
            }
        },
        transactionsStatus: {},
        blocks: {},
        blocksMeta: {},
        entry: {},
        accountsDataSlice: [],
        commitment: CommitmentLevel.PROCESSED, // 指定级别为processed
        ping: undefined,
    };

    // 发送订阅请求
    await new Promise<void>((resolve, reject) => {
        stream.write(request, (err) => {
            if (err === null || err === undefined) {
                resolve();
            } else {
                reject(err);
            }
        });
    }).catch((reason) => {
        console.error(reason);
        throw reason;
    });

    // 获取订阅数据
    stream.on("data", async (data) => {
        if (data.transaction) {
            console.log('start subscribe');
            // console.log(data);
            // 打印签名
            const accountKeys = data.transaction.transaction.transaction.message.accountKeys.map((ak: Uint8Array) => bs58.encode(ak));
            console.log('signer:', accountKeys[0])
            console.log('signature:', bs58.encode(data.transaction.transaction.signature));


            // await checkBalances(data);
            await getFirstMint(data.transaction.transaction.meta.preTokenBalances);


            console.log('end subscribe\n');
        }
    });

    // 为保证连接稳定，需要定期向服务端发送ping请求以维持连接
    const pingRequest: SubscribeRequest = {
        accounts: {},
        slots: {},
        transactions: {},
        transactionsStatus: {},
        blocks: {},
        blocksMeta: {},
        entry: {},
        accountsDataSlice: [],
        commitment: undefined,
        ping: { id: 1 },
    };
    // 每5秒发送一次ping请求
    setInterval(async () => {
        await new Promise<void>((resolve, reject) => {
            stream.write(pingRequest, (err) => {
                if (err === null || err === undefined) {
                    resolve();
                } else {
                    reject(err);
                }
            });
        }).catch((reason) => {
            console.error(reason);
            throw reason;
        });
    }, 5000); 
}

main();