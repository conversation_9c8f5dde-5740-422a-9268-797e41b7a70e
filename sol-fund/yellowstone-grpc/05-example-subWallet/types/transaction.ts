/**
 * 代币金额接口
 * 包含代币金额的不同表示方式
 */
export interface TokenAmount {
    uiAmount: number;      // UI 显示的金额（已考虑小数位）
    decimals: number;      // 代币小数位数
    amount: string;        // 原始金额（大整数字符串）
    uiAmountString: string;// UI 显示金额的字符串形式
}

/**
 * 代币余额接口
 * 描述账户中某个代币的余额信息
 */
export interface TokenBalance {
    accountIndex: number;  // 账户在交易中的索引
    mint: string;         // 代币的 Mint 地址
    uiTokenAmount: TokenAmount;  // 代币金额信息
    owner: string;        // 账户所有者
    programId: string;    // 代币程序 ID
}

/**
 * 交易数据接口
 * 包含完整的交易信息，包括签名、指令和余额变化
 */
export interface TransactionData {
    transaction: {
        slot: number;     // 交易所在的区块槽位
        transaction: {
            signature: Uint8Array;  // 交易签名
            transaction: {
                message: {
                    accountKeys: Uint8Array[];  // 交易涉及的账户公钥列表
                    instructions: {             // 交易包含的指令列表
                        programId: string;      // 程序 ID
                        accounts: number[];     // 指令使用的账户索引
                        data: string;          // 指令数据
                    }[];
                };
            };
            meta: {
                preBalances: number[];         // 交易前的 SOL 余额列表
                postBalances: number[];        // 交易后的 SOL 余额列表
                preTokenBalances: TokenBalance[];   // 交易前的代币余额列表
                postTokenBalances: TokenBalance[]; // 交易后的代币余额列表
            };
        };
    };
}

/**
 * 代币信息接口
 * 用于记录代币的变化情况
 */
export interface TokenInfo {
    mint: string;    // 代币的 Mint 地址
    amount: number;  // 变化的数量（正数表示增加，负数表示减少）
}