import { formatTokenAmount } from './formatter';

/**
 * 交易日志数据接口
 * 定义了需要记录的交易信息字段
 */
export interface TransactionLogData {
    slot: number;      // 交易所在的区块槽位
    wallet: string;    // 交易钱包地址
    // contract: string;  // 交易的合约（如 Raydium, Pump）
    type: string;      // 交易类型（买入/卖出）
    tokenMint: string; // 代币的 Mint 地址
    solAmount: number; // SOL 交易数量
    tokenAmount: number;// 代币交易数量
}

/**
 * 格式化交易输出
 * 将交易数据格式化为美观的日志输出格式
 * @param data 交易日志数据
 * @returns 格式化后的日志字符串
 */
export function formatTransactionOutput(data: TransactionLogData): string {
    return [
        '————————————————————————————',
        `交易槽位：${data.slot}`,
        `交易钱包：${data.wallet}`,
        // `交易合约：${data.contract}`,
        `交易类型：${data.type}`,
        `代币地址：${data.tokenMint}`,
        `SOL数量：${formatTokenAmount(Math.abs(data.solAmount))}`,
        `代币数量：${formatTokenAmount(Math.abs(data.tokenAmount))}`,
        '————————————————————————————\n'
    ].join('\n');
}