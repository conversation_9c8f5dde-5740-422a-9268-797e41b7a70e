import { Connection, PublicKey } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID } from '@solana/spl-token';
import * as fs from 'fs';
import * as path from 'path';

// Solana主网RPC端点
const RPC_ENDPOINT = 'https://rpc.ironforge.network/mainnet?apiKey=01JDQ4WPB0RKDKVKPMMR2DY7F1';
const connection = new Connection(RPC_ENDPOINT);
const CACHE_FILE = './processedTxIds.json';
const walletAddress = new PublicKey('CCeRxvW2bQYYbLxqcjeAZsddjbaeg2ei2tQueewH5ofK'); 
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));
// 从缓存文件读取已处理的 tx ID
const readCache = () => {
  if (fs.existsSync(CACHE_FILE)) {
    const data = fs.readFileSync(CACHE_FILE, { encoding: 'utf-8' });
    return JSON.parse(data);
  }
  return [];
};
// 将新的 tx ID 写入缓存文件
const writeCache = (txIds) => {
  try {
    const dir = path.dirname(CACHE_FILE); // 获取目录名
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true }); // 创建目录（如果不存在）
    }
    
    fs.writeFileSync(CACHE_FILE, JSON.stringify(txIds, null, 2));
    console.log('缓存文件已更新:', CACHE_FILE);
  } catch (error) {
    console.error('写入缓存时出错:', error);
  }
};

const fetchRecentTokenPurchases = async () => {
  try {
    console.log('start...');
	const processedTxIds = readCache();

    // 获取最近的签名列表
    const signatures = await connection.getSignaturesForAddress(walletAddress, { limit: 5 });


    for (const signatureInfo of signatures) {

      const txId = signatureInfo.signature;
	  console.log(`处理 tx ID: ${txId}`);

      // 如果 tx ID 已处理，则跳过
      if (processedTxIds.includes(txId)) {
        console.log(`跳过已处理的 tx ID: ${txId}`);
        continue;
      }
      const transaction = await connection.getTransaction(signatureInfo.signature, {
        commitment: 'confirmed',
        maxSupportedTransactionVersion: 0,
      });


      console.log("签名"+ signatureInfo.signature );
      if (transaction && transaction.meta && transaction.meta.postTokenBalances) {
	    //console.log("Transaction details:", JSON.stringify(transaction, null, 2));
		const logMessages = transaction.meta.logMessages;
	  const openPositionEventDetected = logMessages.some(log => log.includes("OpenPositionV2"));

	  if (openPositionEventDetected) {
		console.log("检测到 OpenPositionV2 事件");
	  } else {
		console.log("未检测到 OpenPositionV2 事件");
		processedTxIds.push(txId);
		  writeCache(processedTxIds);
		  await delay(500);
		continue
	  }
        transaction.meta.postTokenBalances.forEach((balance) => {
          if (balance.owner && balance.mint) {
            const mintAddress = balance.mint;
            console.log("合约地址"+ mintAddress+ "\n");
            
          }
        });
      }
	  processedTxIds.push(txId);
      writeCache(processedTxIds);
	  await delay(500);
    }


    console.log('查询完成。');
  } catch (error) {
    console.error('查询过程中出现错误:', error);
  }
};

// 循环调用函数
const startFetching = async () => {
  while (true) {
    await fetchRecentTokenPurchases();
    await delay(5000); // 每隔5秒调用一次
  }
};

// 调用循环函数
startFetching();
