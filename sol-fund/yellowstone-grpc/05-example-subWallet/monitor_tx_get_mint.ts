import { Connection, PublicKey } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID } from '@solana/spl-token';
import * as fs from 'fs';
import * as path from 'path';
import sqlite3 from 'sqlite3';
import express from 'express';

// 创建数据库连接
const db = new sqlite3.Database('rpc_mints.db');

// 初始化数据库表
db.serialize(() => {
  // 删除旧表（如果存在）
  db.run(`DROP TABLE IF EXISTS mints`);
  
  // 创建新表
  db.run(`
    CREATE TABLE mints (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      mint_id TEXT NOT NULL UNIQUE,
      count INTEGER DEFAULT 1,
      first_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_seen DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);
});

// Solana主网RPC端点
const RPC_ENDPOINT = 'https://greatest-old-orb.solana-mainnet.quiknode.pro/4fa5da189e41f0637dad36ebd8ea5f870cb254d5';
const connection = new Connection(RPC_ENDPOINT);
const CACHE_FILE = './processedTxIds.json';
const walletAddress = new PublicKey('Arb1wtZQbbxEuC1D4WD4Lk2ix6xv9KetB7CHdiuiMc6B'); 
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));


// 存储或更新 mint 到数据库
async function storeMintToDb(mint: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const query = `
      INSERT INTO mints (mint_id, count, last_seen) 
      VALUES (?, 1, CURRENT_TIMESTAMP)
      ON CONFLICT(mint_id) DO UPDATE SET 
        count = count + 1,
        last_seen = CURRENT_TIMESTAMP
    `;
    
    db.run(query, [mint], (err) => {
      if (err) {
        console.error('Error storing/updating mint:', err);
        reject(err);
      } else {
        resolve();
      }
    });
  });
}

// 从缓存文件读取已处理的 tx ID
const readCache = () => {
  if (fs.existsSync(CACHE_FILE)) {
    const data = fs.readFileSync(CACHE_FILE, { encoding: 'utf-8' });
    return JSON.parse(data);
  }
  return [];
};
// 将新的 tx ID 写入缓存文件
const writeCache = (txIds) => {
  try {
    const dir = path.dirname(CACHE_FILE); // 获取目录名
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true }); // 创建目录（如果不存在）
    }
    
    fs.writeFileSync(CACHE_FILE, JSON.stringify(txIds, null, 2));
    console.log('缓存文件已更新:', CACHE_FILE);
  } catch (error) {
    console.error('写入缓存时出错:', error);
  }
};

function getFirstMint(preBalances) {
  for (const balance of preBalances) {
    if (balance.mint !== "So11111111111111111111111111111111111111112") {
      console.log("取得的mint:", balance.mint);
      // 存储到数据库
       storeMintToDb(balance.mint);
      return balance.mint;
    }
  }
  return null; // 如果没有找到合适的 mint 值
}

const fetchRecentTokenPurchases = async () => {
  try {
    console.log('start...');
	const processedTxIds = readCache();

    // 获取最近的签名列表
    const signatures = await connection.getSignaturesForAddress(walletAddress, { limit: 1 });


    for (const signatureInfo of signatures) {

      const txId = signatureInfo.signature;
	    console.log(`处理 tx ID: ${txId}`);

      // 如果 tx ID 已处理，则跳过
      if (processedTxIds.includes(txId)) {
        console.log(`跳过已处理的 tx ID: ${txId}`);
        continue;
      }
      const transaction = await connection.getTransaction(signatureInfo.signature, {
        commitment: 'confirmed',
        maxSupportedTransactionVersion: 0,
      });


      if (transaction && transaction.meta && transaction.meta.postTokenBalances) {
      // 打印整个交易信息
	    // console.log("Transaction details:", JSON.stringify(transaction, null, 2));
      getFirstMint(transaction.meta.postTokenBalances)
	
        // transaction.meta.postTokenBalances.forEach((balance) => {
        //   if (balance.owner && balance.mint) {
        //     const mintAddress = balance.mint;
        //     console.log("合约地址"+ mintAddress+ "\n");
            
        //   }
        // });
      }
	  processedTxIds.push(txId);
      writeCache(processedTxIds);
	  await delay(500);
    }


    console.log('查询完成。');
  } catch (error) {
    console.error('查询过程中出现错误:', error);
  }
};

// 循环调用函数
const startFetching = async () => {
  while (true) {
    await fetchRecentTokenPurchases();
    await delay(5000); // 每隔5秒调用一次
  }
};

// 调用循环函数
startFetching();


// todo 还需要展示接口，脚本还需要优化吗？一定要稳定，考虑好这些容错性不能崩溃，然后打包成可执行文件
