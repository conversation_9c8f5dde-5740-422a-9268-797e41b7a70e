{"name": "stream-and-parse-pump-fun-transaction-instructions-via-grpc", "version": "1.0.0", "description": "", "main": "index.ts", "type": "commonjs", "scripts": {"start": "npx ts-node index.ts", "build": "npx tsc"}, "author": "shyft-to", "keywords": ["solana", "grpc", "pump.fun", "amm", "transaction", "parser", "typescript", "blockchain", "nodejs", "web3"], "license": "ISC", "devDependencies": {"@types/lodash": "^4.17.4", "@types/node": "^20.10.0", "ts-node-dev": "^2.0.0", "tsx": "^4.7.1", "typescript": "^5.4.5"}, "dependencies": {"@coral-xyz/anchor": "^0.30.1", "@shyft-to/solana-transaction-parser": "^2.0.0", "@solana/web3.js": "^1.93.0", "@triton-one/yellowstone-grpc": "^4.0.0", "dotenv": "^16.4.7", "lodash": "^4.17.21"}}