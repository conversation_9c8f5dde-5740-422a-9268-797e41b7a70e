# Subscribe to Real-Time Updates for Newly Minted Tokens on PumpFun Using gRPC in Rust

Stay ahead of the curve by leveraging gRPC to stream real-time updates for newly minted tokens on PumpFun. This powerful integration allows you to monitor the platform for the latest token launches as soon as they happen. By implementing this solution in Rust, you can ensure high performance, reliability, and efficiency in handling real-time data streams.

With this setup, you’ll be the first to know when a new token is minted on PumpFun, giving you a competitive edge in tracking and analyzing emerging opportunities in the crypto space. 
Whether you're building a trading bot, conducting market research, or simply staying informed, 
this gRPC-based solution provides a robust and scalable way to stay updated on the latest token launches.


```
 cargo run -- --endpoint https://grpc.ny.shyft.to --x-token<token>

--PS: this code only works for the program ID <address>
-- DON'T attempt to use the code for any other program
## Notes

gRPC client examples :https://github.com/Shyft-to/solana-defi
Blogs : blogs.shyft.to
Learn about shyft: https://shyft.to/
Discord: https://discord.gg/6bSmYuDa