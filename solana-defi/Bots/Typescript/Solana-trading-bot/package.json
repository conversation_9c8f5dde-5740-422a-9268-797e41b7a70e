{"dependencies": {"@jup-ag/api": "^6.0.11", "@jup-ag/core": "^4.0.0-beta.21", "@native-to-anchor/buffer-layout": "^0.1.0", "@project-serum/anchor": "^0.26.0", "@project-serum/serum": "^0.13.65", "@raydium-io/raydium-sdk": "^1.3.1-beta.50", "@shyft-to/js": "^0.2.36", "@solana/spl-token": "^0.4.3", "@solana/spl-token-registry": "^0.2.4574", "@solana/spl-token-swap": "^0.4.0", "@solana/web3.js": "^1.90.0", "@triton-one/yellowstone-grpc": "^0.3.0", "@types/npm": "^7.19.3", "bs58": "^5.0.0", "cheerio": "^1.0.0-rc.12", "cross-fetch": "^4.0.0", "dotenv": "^16.3.1", "ethers": "^6.2.3", "express": "^4.18.2", "graphql-request": "^6.1.0", "jito-ts": "^3.0.1", "ngrok": "^5.0.0-beta.2", "node-telegram-bot-api": "^0.64.0", "nodemon": "^3.0.3", "telegraf": "^4.16.3"}, "scripts": {"tsc.cmd": "tsc", "start": "nodemon index.ts"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.11.25", "truffle": "^5.11.5", "typescript": "^4.7.4"}, "config": {"bsc": {"networkId": 56, "gasPrice": 25000000000, "gasLimit": 6500000}}}