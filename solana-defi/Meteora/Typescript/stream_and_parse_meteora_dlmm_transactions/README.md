<a id="readme-top"></a>

# Streaming Meteora DLMM transaction using gRPC and parsing instructions in Typescript

This project streams real-time Meteora transactions via gRPC, efficiently parsing and decoding DLMM instructions. It extracts key insights from transaction data and structures parsed instructions into a serialized format for seamless processing and analysis. Optimized for high-throughput performance, the implementation leverages Rust’s type safety and efficiency, ensuring smooth integration with the Solana ecosystem.

![screenshot](assets/meteora_dlmm_screenshot.png?raw=true "Screenshot")

## Getting Started

1. **Clone the repository:**
   ```bash
   git clone https://github.com/Shyft-to/solana-defi.git
   cd PumpFun/Typescript/stream_and_parse_meteora_dlmm_instructions
   ```

2. **Install Dependencies:**

    ```bash
    # For example, if using npm
    npm i
    ```

3. **Run the script:**

    ```bash
    # To run the script
    npm run start
    ```

*Note: Please open `.env` and input your env details before running the script.*

## Related Links

Shyft gRPC Docs: [https://docs.shyft.to/solana-fast-grpc/grpc-docs]