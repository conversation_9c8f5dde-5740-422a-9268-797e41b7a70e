{"version": "0.9.0", "name": "lb_clmm", "constants": [{"name": "BASIS_POINT_MAX", "type": "i32", "value": "10000"}, {"name": "MAX_BIN_PER_ARRAY", "type": {"defined": "usize"}, "value": "70"}, {"name": "MAX_BIN_PER_POSITION", "type": {"defined": "usize"}, "value": "70"}, {"name": "MAX_RESIZE_LENGTH", "type": {"defined": "usize"}, "value": "70"}, {"name": "POSITION_MAX_LENGTH", "type": {"defined": "usize"}, "value": "1400"}, {"name": "MIN_BIN_ID", "type": "i32", "value": "- 443636"}, {"name": "MAX_BIN_ID", "type": "i32", "value": "443636"}, {"name": "MAX_FEE_RATE", "type": "u64", "value": "100_000_000"}, {"name": "FEE_PRECISION", "type": "u64", "value": "1_000_000_000"}, {"name": "MAX_PROTOCOL_SHARE", "type": "u16", "value": "2_500"}, {"name": "HOST_FEE_BPS", "type": "u16", "value": "2_000"}, {"name": "NUM_REWARDS", "type": {"defined": "usize"}, "value": "2"}, {"name": "MIN_REWARD_DURATION", "type": "u64", "value": "1"}, {"name": "MAX_REWARD_DURATION", "type": "u64", "value": "31536000"}, {"name": "EXTENSION_BINARRAY_BITMAP_SIZE", "type": {"defined": "usize"}, "value": "12"}, {"name": "BIN_ARRAY_BITMAP_SIZE", "type": "i32", "value": "512"}, {"name": "MAX_REWARD_BIN_SPLIT", "type": {"defined": "usize"}, "value": "15"}, {"name": "ILM_PROTOCOL_SHARE", "type": "u16", "value": "2000"}, {"name": "PROTOCOL_SHARE", "type": "u16", "value": "500"}, {"name": "MAX_BIN_STEP", "type": "u16", "value": "400"}, {"name": "MAX_BASE_FEE", "type": "u128", "value": "100_000_000"}, {"name": "MIN_BASE_FEE", "type": "u128", "value": "100_000"}, {"name": "MINIMUM_LIQUIDITY", "type": "u128", "value": "1_000_000"}, {"name": "BIN_ARRAY", "type": "bytes", "value": "[98, 105, 110, 95, 97, 114, 114, 97, 121]"}, {"name": "ORACLE", "type": "bytes", "value": "[111, 114, 97, 99, 108, 101]"}, {"name": "BIN_ARRAY_BITMAP_SEED", "type": "bytes", "value": "[98, 105, 116, 109, 97, 112]"}, {"name": "PRESET_PARAMETER", "type": "bytes", "value": "[112, 114, 101, 115, 101, 116, 95, 112, 97, 114, 97, 109, 101, 116, 101, 114]"}, {"name": "PRESET_PARAMETER2", "type": "bytes", "value": "[112, 114, 101, 115, 101, 116, 95, 112, 97, 114, 97, 109, 101, 116, 101, 114, 50]"}, {"name": "POSITION", "type": "bytes", "value": "[112, 111, 115, 105, 116, 105, 111, 110]"}, {"name": "CLAIM_PROTOCOL_FEE_OPERATOR", "type": "bytes", "value": "[99, 102, 95, 111, 112, 101, 114, 97, 116, 111, 114]"}], "instructions": [{"name": "initializeLbPair", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "tokenMintX", "isMut": false, "isSigner": false}, {"name": "tokenMintY", "isMut": false, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "oracle", "isMut": true, "isSigner": false}, {"name": "presetParameter", "isMut": false, "isSigner": false}, {"name": "funder", "isMut": true, "isSigner": true}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "activeId", "type": "i32"}, {"name": "binStep", "type": "u16"}]}, {"name": "initializePermissionLbPair", "accounts": [{"name": "base", "isMut": false, "isSigner": true}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "tokenMintX", "isMut": false, "isSigner": false}, {"name": "tokenMintY", "isMut": false, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "oracle", "isMut": true, "isSigner": false}, {"name": "admin", "isMut": true, "isSigner": true}, {"name": "tokenBadgeX", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "tokenBadgeY", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "tokenProgramX", "isMut": false, "isSigner": false}, {"name": "tokenProgramY", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "ixData", "type": {"defined": "InitPermissionPairIx"}}]}, {"name": "initializeCustomizablePermissionlessLbPair", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "tokenMintX", "isMut": false, "isSigner": false}, {"name": "tokenMintY", "isMut": false, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "oracle", "isMut": true, "isSigner": false}, {"name": "userTokenX", "isMut": false, "isSigner": false}, {"name": "funder", "isMut": true, "isSigner": true}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "userTokenY", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "params", "type": {"defined": "CustomizableParams"}}]}, {"name": "initializeBinArrayBitmapExtension", "accounts": [{"name": "lbPair", "isMut": false, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "docs": ["Initialize an account to store if a bin array is initialized."]}, {"name": "funder", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}], "args": []}, {"name": "initializeBinArray", "accounts": [{"name": "lbPair", "isMut": false, "isSigner": false}, {"name": "binArray", "isMut": true, "isSigner": false}, {"name": "funder", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "index", "type": "i64"}]}, {"name": "addLiquidity", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "userTokenX", "isMut": true, "isSigner": false}, {"name": "userTokenY", "isMut": true, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "binArrayLower", "isMut": true, "isSigner": false}, {"name": "binArrayUpper", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityParameter", "type": {"defined": "LiquidityParameter"}}]}, {"name": "addLiquidityByWeight", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "userTokenX", "isMut": true, "isSigner": false}, {"name": "userTokenY", "isMut": true, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "binArrayLower", "isMut": true, "isSigner": false}, {"name": "binArrayUpper", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityParameter", "type": {"defined": "LiquidityParameterByWeight"}}]}, {"name": "addLiquidityByStrategy", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "userTokenX", "isMut": true, "isSigner": false}, {"name": "userTokenY", "isMut": true, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "binArrayLower", "isMut": true, "isSigner": false}, {"name": "binArrayUpper", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityParameter", "type": {"defined": "LiquidityParameterByStrategy"}}]}, {"name": "addLiquidityByStrategyOneSide", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "userToken", "isMut": true, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "tokenMint", "isMut": false, "isSigner": false}, {"name": "binArrayLower", "isMut": true, "isSigner": false}, {"name": "binArrayUpper", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityParameter", "type": {"defined": "LiquidityParameterByStrategyOneSide"}}]}, {"name": "addLiquidityOneSide", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "userToken", "isMut": true, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "tokenMint", "isMut": false, "isSigner": false}, {"name": "binArrayLower", "isMut": true, "isSigner": false}, {"name": "binArrayUpper", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityParameter", "type": {"defined": "LiquidityOneSideParameter"}}]}, {"name": "removeLiquidity", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "userTokenX", "isMut": true, "isSigner": false}, {"name": "userTokenY", "isMut": true, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "binArrayLower", "isMut": true, "isSigner": false}, {"name": "binArrayUpper", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "binLiquidityRemoval", "type": {"vec": {"defined": "BinLiquidityReduction"}}}]}, {"name": "initializePosition", "accounts": [{"name": "payer", "isMut": true, "isSigner": true}, {"name": "position", "isMut": true, "isSigner": true}, {"name": "lbPair", "isMut": false, "isSigner": false}, {"name": "owner", "isMut": false, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "lowerBinId", "type": "i32"}, {"name": "width", "type": "i32"}]}, {"name": "initializePositionPda", "accounts": [{"name": "payer", "isMut": true, "isSigner": true}, {"name": "base", "isMut": false, "isSigner": true}, {"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": false, "isSigner": false}, {"name": "owner", "isMut": false, "isSigner": true, "docs": ["owner"]}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "lowerBinId", "type": "i32"}, {"name": "width", "type": "i32"}]}, {"name": "initializePositionByOperator", "accounts": [{"name": "payer", "isMut": true, "isSigner": true}, {"name": "base", "isMut": false, "isSigner": true}, {"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": false, "isSigner": false}, {"name": "owner", "isMut": false, "isSigner": false}, {"name": "operator", "isMut": false, "isSigner": true, "docs": ["operator"]}, {"name": "operatorTokenX", "isMut": false, "isSigner": false}, {"name": "ownerTokenX", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "lowerBinId", "type": "i32"}, {"name": "width", "type": "i32"}, {"name": "feeOwner", "type": "public<PERSON>ey"}, {"name": "lockReleasePoint", "type": "u64"}]}, {"name": "updatePositionOperator", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "owner", "isMut": false, "isSigner": true}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "operator", "type": "public<PERSON>ey"}]}, {"name": "swap", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "userTokenIn", "isMut": true, "isSigner": false}, {"name": "userTokenOut", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "oracle", "isMut": true, "isSigner": false}, {"name": "hostFeeIn", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "user", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "amountIn", "type": "u64"}, {"name": "minAmountOut", "type": "u64"}]}, {"name": "swapExactOut", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "userTokenIn", "isMut": true, "isSigner": false}, {"name": "userTokenOut", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "oracle", "isMut": true, "isSigner": false}, {"name": "hostFeeIn", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "user", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "maxInAmount", "type": "u64"}, {"name": "outAmount", "type": "u64"}]}, {"name": "swapWithPriceImpact", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "userTokenIn", "isMut": true, "isSigner": false}, {"name": "userTokenOut", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "oracle", "isMut": true, "isSigner": false}, {"name": "hostFeeIn", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "user", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "amountIn", "type": "u64"}, {"name": "activeId", "type": {"option": "i32"}}, {"name": "maxPriceImpactBps", "type": "u16"}]}, {"name": "withdrawProtocolFee", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "receiverTokenX", "isMut": true, "isSigner": false}, {"name": "receiverTokenY", "isMut": true, "isSigner": false}, {"name": "claimFeeOperator", "isMut": false, "isSigner": false}, {"name": "operator", "isMut": false, "isSigner": true, "docs": ["operator"]}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "memoProgram", "isMut": false, "isSigner": false}], "args": [{"name": "amountX", "type": "u64"}, {"name": "amountY", "type": "u64"}, {"name": "remainingAccountsInfo", "type": {"defined": "RemainingAccountsInfo"}}]}, {"name": "initializeReward", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "rewardVault", "isMut": true, "isSigner": false}, {"name": "rewardMint", "isMut": false, "isSigner": false}, {"name": "tokenBadge", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "admin", "isMut": true, "isSigner": true}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "rewardIndex", "type": "u64"}, {"name": "rewardDuration", "type": "u64"}, {"name": "funder", "type": "public<PERSON>ey"}]}, {"name": "fundReward", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "rewardVault", "isMut": true, "isSigner": false}, {"name": "rewardMint", "isMut": false, "isSigner": false}, {"name": "funderTokenAccount", "isMut": true, "isSigner": false}, {"name": "funder", "isMut": false, "isSigner": true}, {"name": "binArray", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "rewardIndex", "type": "u64"}, {"name": "amount", "type": "u64"}, {"name": "carryForward", "type": "bool"}, {"name": "remainingAccountsInfo", "type": {"defined": "RemainingAccountsInfo"}}]}, {"name": "updateRewardFunder", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "admin", "isMut": false, "isSigner": true}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "rewardIndex", "type": "u64"}, {"name": "new<PERSON><PERSON><PERSON>", "type": "public<PERSON>ey"}]}, {"name": "updateRewardDuration", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "admin", "isMut": false, "isSigner": true}, {"name": "binArray", "isMut": true, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "rewardIndex", "type": "u64"}, {"name": "newDuration", "type": "u64"}]}, {"name": "claimReward", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "position", "isMut": true, "isSigner": false}, {"name": "binArrayLower", "isMut": true, "isSigner": false}, {"name": "binArrayUpper", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "rewardVault", "isMut": true, "isSigner": false}, {"name": "rewardMint", "isMut": false, "isSigner": false}, {"name": "userTokenAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "rewardIndex", "type": "u64"}]}, {"name": "claimFee", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "position", "isMut": true, "isSigner": false}, {"name": "binArrayLower", "isMut": true, "isSigner": false}, {"name": "binArrayUpper", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "userTokenX", "isMut": true, "isSigner": false}, {"name": "userTokenY", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": []}, {"name": "closePosition", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayLower", "isMut": true, "isSigner": false}, {"name": "binArrayUpper", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "rentReceiver", "isMut": true, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": []}, {"name": "updateBaseFeeParameters", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "admin", "isMut": false, "isSigner": true}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "feeParameter", "type": {"defined": "BaseFeeParameter"}}]}, {"name": "updateDynamicFeeParameters", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "admin", "isMut": false, "isSigner": true}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "feeParameter", "type": {"defined": "DynamicFeeParameter"}}]}, {"name": "increaseOracleLength", "accounts": [{"name": "oracle", "isMut": true, "isSigner": false}, {"name": "funder", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "lengthToAdd", "type": "u64"}]}, {"name": "initializePresetParameter", "accounts": [{"name": "presetParameter", "isMut": true, "isSigner": false}, {"name": "admin", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rent", "isMut": false, "isSigner": false}], "args": [{"name": "ix", "type": {"defined": "InitPresetParametersIx"}}]}, {"name": "closePresetParameter", "accounts": [{"name": "presetParameter", "isMut": true, "isSigner": false}, {"name": "admin", "isMut": true, "isSigner": true}, {"name": "rentReceiver", "isMut": true, "isSigner": false}], "args": []}, {"name": "closePresetParameter2", "accounts": [{"name": "presetParameter", "isMut": true, "isSigner": false}, {"name": "admin", "isMut": true, "isSigner": true}, {"name": "rentReceiver", "isMut": true, "isSigner": false}], "args": []}, {"name": "removeAllLiquidity", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "userTokenX", "isMut": true, "isSigner": false}, {"name": "userTokenY", "isMut": true, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "binArrayLower", "isMut": true, "isSigner": false}, {"name": "binArrayUpper", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": []}, {"name": "setPairStatus", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "admin", "isMut": false, "isSigner": true}], "args": [{"name": "status", "type": "u8"}]}, {"name": "migratePosition", "accounts": [{"name": "positionV2", "isMut": true, "isSigner": true}, {"name": "positionV1", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": false, "isSigner": false}, {"name": "binArrayLower", "isMut": true, "isSigner": false}, {"name": "binArrayUpper", "isMut": true, "isSigner": false}, {"name": "owner", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "rentReceiver", "isMut": true, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": []}, {"name": "migrateBinArray", "accounts": [{"name": "lbPair", "isMut": false, "isSigner": false}], "args": []}, {"name": "updateFeesAndRewards", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayLower", "isMut": true, "isSigner": false}, {"name": "binArrayUpper", "isMut": true, "isSigner": false}, {"name": "owner", "isMut": false, "isSigner": true}], "args": []}, {"name": "withdrawIneligibleReward", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "rewardVault", "isMut": true, "isSigner": false}, {"name": "rewardMint", "isMut": false, "isSigner": false}, {"name": "funderTokenAccount", "isMut": true, "isSigner": false}, {"name": "funder", "isMut": false, "isSigner": true}, {"name": "binArray", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "memoProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "rewardIndex", "type": "u64"}, {"name": "remainingAccountsInfo", "type": {"defined": "RemainingAccountsInfo"}}]}, {"name": "setActivationPoint", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "admin", "isMut": true, "isSigner": true}], "args": [{"name": "activationPoint", "type": "u64"}]}, {"name": "removeLiquidityByRange", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "userTokenX", "isMut": true, "isSigner": false}, {"name": "userTokenY", "isMut": true, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "binArrayLower", "isMut": true, "isSigner": false}, {"name": "binArrayUpper", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "fromBinId", "type": "i32"}, {"name": "toBinId", "type": "i32"}, {"name": "bpsToRemove", "type": "u16"}]}, {"name": "addLiquidityOneSidePrecise", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "userToken", "isMut": true, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "tokenMint", "isMut": false, "isSigner": false}, {"name": "binArrayLower", "isMut": true, "isSigner": false}, {"name": "binArrayUpper", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "parameter", "type": {"defined": "AddLiquiditySingleSidePreciseParameter"}}]}, {"name": "goToABin", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "fromBinArray", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "toBinArray", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "binId", "type": "i32"}]}, {"name": "setPreActivationDuration", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "creator", "isMut": false, "isSigner": true}], "args": [{"name": "preActivationDuration", "type": "u64"}]}, {"name": "setPreActivationSwapAddress", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "creator", "isMut": false, "isSigner": true}], "args": [{"name": "preActivationSwapAddress", "type": "public<PERSON>ey"}]}, {"name": "setPairStatusPermissionless", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "creator", "isMut": false, "isSigner": true}], "args": [{"name": "status", "type": "u8"}]}, {"name": "initializeTokenBadge", "accounts": [{"name": "tokenMint", "isMut": false, "isSigner": false}, {"name": "tokenBadge", "isMut": true, "isSigner": false}, {"name": "admin", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "createClaimProtocolFeeOperator", "accounts": [{"name": "claimFeeOperator", "isMut": true, "isSigner": false}, {"name": "operator", "isMut": false, "isSigner": false}, {"name": "admin", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": []}, {"name": "closeClaimProtocolFeeOperator", "accounts": [{"name": "claimFeeOperator", "isMut": true, "isSigner": false}, {"name": "rentReceiver", "isMut": true, "isSigner": false}, {"name": "admin", "isMut": false, "isSigner": true}], "args": []}, {"name": "initializePresetParameter2", "accounts": [{"name": "presetParameter", "isMut": true, "isSigner": false}, {"name": "admin", "isMut": true, "isSigner": true}, {"name": "systemProgram", "isMut": false, "isSigner": false}], "args": [{"name": "ix", "type": {"defined": "InitPresetParameters2Ix"}}]}, {"name": "initializeLbPair2", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "tokenMintX", "isMut": false, "isSigner": false}, {"name": "tokenMintY", "isMut": false, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "oracle", "isMut": true, "isSigner": false}, {"name": "presetParameter", "isMut": false, "isSigner": false}, {"name": "funder", "isMut": true, "isSigner": true}, {"name": "tokenBadgeX", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "tokenBadgeY", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "tokenProgramX", "isMut": false, "isSigner": false}, {"name": "tokenProgramY", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "params", "type": {"defined": "InitializeLbPair2Params"}}]}, {"name": "initializeCustomizablePermissionlessLbPair2", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "tokenMintX", "isMut": false, "isSigner": false}, {"name": "tokenMintY", "isMut": false, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "oracle", "isMut": true, "isSigner": false}, {"name": "userTokenX", "isMut": false, "isSigner": false}, {"name": "funder", "isMut": true, "isSigner": true}, {"name": "tokenBadgeX", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "tokenBadgeY", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "tokenProgramX", "isMut": false, "isSigner": false}, {"name": "tokenProgramY", "isMut": false, "isSigner": false}, {"name": "systemProgram", "isMut": false, "isSigner": false}, {"name": "userTokenY", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "params", "type": {"defined": "CustomizableParams"}}]}, {"name": "claimFee2", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "position", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "userTokenX", "isMut": true, "isSigner": false}, {"name": "userTokenY", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "tokenProgramX", "isMut": false, "isSigner": false}, {"name": "tokenProgramY", "isMut": false, "isSigner": false}, {"name": "memoProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "minBinId", "type": "i32"}, {"name": "maxBinId", "type": "i32"}, {"name": "remainingAccountsInfo", "type": {"defined": "RemainingAccountsInfo"}}]}, {"name": "claimReward2", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "position", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "rewardVault", "isMut": true, "isSigner": false}, {"name": "rewardMint", "isMut": false, "isSigner": false}, {"name": "userTokenAccount", "isMut": true, "isSigner": false}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "memoProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "rewardIndex", "type": "u64"}, {"name": "minBinId", "type": "i32"}, {"name": "maxBinId", "type": "i32"}, {"name": "remainingAccountsInfo", "type": {"defined": "RemainingAccountsInfo"}}]}, {"name": "addLiquidity2", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "userTokenX", "isMut": true, "isSigner": false}, {"name": "userTokenY", "isMut": true, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityParameter", "type": {"defined": "LiquidityParameter"}}, {"name": "remainingAccountsInfo", "type": {"defined": "RemainingAccountsInfo"}}]}, {"name": "addLiquidityByStrategy2", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "userTokenX", "isMut": true, "isSigner": false}, {"name": "userTokenY", "isMut": true, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityParameter", "type": {"defined": "LiquidityParameterByStrategy"}}, {"name": "remainingAccountsInfo", "type": {"defined": "RemainingAccountsInfo"}}]}, {"name": "addLiquidityOneSidePrecise2", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "userToken", "isMut": true, "isSigner": false}, {"name": "reserve", "isMut": true, "isSigner": false}, {"name": "tokenMint", "isMut": false, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "tokenProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "liquidityParameter", "type": {"defined": "AddLiquiditySingleSidePreciseParameter2"}}, {"name": "remainingAccountsInfo", "type": {"defined": "RemainingAccountsInfo"}}]}, {"name": "removeLiquidity2", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "userTokenX", "isMut": true, "isSigner": false}, {"name": "userTokenY", "isMut": true, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "memoProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "binLiquidityRemoval", "type": {"vec": {"defined": "BinLiquidityReduction"}}}, {"name": "remainingAccountsInfo", "type": {"defined": "RemainingAccountsInfo"}}]}, {"name": "removeLiquidityByRange2", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "userTokenX", "isMut": true, "isSigner": false}, {"name": "userTokenY", "isMut": true, "isSigner": false}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "memoProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "fromBinId", "type": "i32"}, {"name": "toBinId", "type": "i32"}, {"name": "bpsToRemove", "type": "u16"}, {"name": "remainingAccountsInfo", "type": {"defined": "RemainingAccountsInfo"}}]}, {"name": "swap2", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "userTokenIn", "isMut": true, "isSigner": false}, {"name": "userTokenOut", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "oracle", "isMut": true, "isSigner": false}, {"name": "hostFeeIn", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "user", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "memoProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "amountIn", "type": "u64"}, {"name": "minAmountOut", "type": "u64"}, {"name": "remainingAccountsInfo", "type": {"defined": "RemainingAccountsInfo"}}]}, {"name": "swapExactOut2", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "userTokenIn", "isMut": true, "isSigner": false}, {"name": "userTokenOut", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "oracle", "isMut": true, "isSigner": false}, {"name": "hostFeeIn", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "user", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "memoProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "maxInAmount", "type": "u64"}, {"name": "outAmount", "type": "u64"}, {"name": "remainingAccountsInfo", "type": {"defined": "RemainingAccountsInfo"}}]}, {"name": "swapWithPriceImpact2", "accounts": [{"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "binArrayBitmapExtension", "isMut": false, "isSigner": false, "isOptional": true}, {"name": "reserveX", "isMut": true, "isSigner": false}, {"name": "reserveY", "isMut": true, "isSigner": false}, {"name": "userTokenIn", "isMut": true, "isSigner": false}, {"name": "userTokenOut", "isMut": true, "isSigner": false}, {"name": "tokenXMint", "isMut": false, "isSigner": false}, {"name": "tokenYMint", "isMut": false, "isSigner": false}, {"name": "oracle", "isMut": true, "isSigner": false}, {"name": "hostFeeIn", "isMut": true, "isSigner": false, "isOptional": true}, {"name": "user", "isMut": false, "isSigner": true}, {"name": "tokenXProgram", "isMut": false, "isSigner": false}, {"name": "tokenYProgram", "isMut": false, "isSigner": false}, {"name": "memoProgram", "isMut": false, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": [{"name": "amountIn", "type": "u64"}, {"name": "activeId", "type": {"option": "i32"}}, {"name": "maxPriceImpactBps", "type": "u16"}, {"name": "remainingAccountsInfo", "type": {"defined": "RemainingAccountsInfo"}}]}, {"name": "closePosition2", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "rentReceiver", "isMut": true, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": []}, {"name": "updateFeesAndReward2", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "lbPair", "isMut": true, "isSigner": false}, {"name": "owner", "isMut": false, "isSigner": true}], "args": [{"name": "minBinId", "type": "i32"}, {"name": "maxBinId", "type": "i32"}]}, {"name": "closePositionIfEmpty", "accounts": [{"name": "position", "isMut": true, "isSigner": false}, {"name": "sender", "isMut": false, "isSigner": true}, {"name": "rentReceiver", "isMut": true, "isSigner": false}, {"name": "eventAuthority", "isMut": false, "isSigner": false}, {"name": "program", "isMut": false, "isSigner": false}], "args": []}], "accounts": [{"name": "BinArrayBitmapExtension", "type": {"kind": "struct", "fields": [{"name": "lbPair", "type": "public<PERSON>ey"}, {"name": "positiveBinArrayBitmap", "docs": ["Packed initialized bin array state for start_bin_index is positive"], "type": {"array": [{"array": ["u64", 8]}, 12]}}, {"name": "negativeBinArrayBitmap", "docs": ["Packed initialized bin array state for start_bin_index is negative"], "type": {"array": [{"array": ["u64", 8]}, 12]}}]}}, {"name": "BinArray", "docs": ["An account to contain a range of bin. For example: Bin 100 <-> 200.", "For example:", "BinArray index: 0 contains bin 0 <-> 599", "index: 2 contains bin 600 <-> 1199, ..."], "type": {"kind": "struct", "fields": [{"name": "index", "type": "i64"}, {"name": "version", "docs": ["Version of binArray"], "type": "u8"}, {"name": "padding", "type": {"array": ["u8", 7]}}, {"name": "lbPair", "type": "public<PERSON>ey"}, {"name": "bins", "type": {"array": [{"defined": "Bin"}, 70]}}]}}, {"name": "ClaimFeeOperator", "docs": ["Parameter that set by the protocol"], "type": {"kind": "struct", "fields": [{"name": "operator", "docs": ["operator"], "type": "public<PERSON>ey"}, {"name": "padding", "docs": ["Reserve"], "type": {"array": ["u8", 128]}}]}}, {"name": "LbPair", "type": {"kind": "struct", "fields": [{"name": "parameters", "type": {"defined": "StaticParameters"}}, {"name": "vParameters", "type": {"defined": "VariableParameters"}}, {"name": "bumpSeed", "type": {"array": ["u8", 1]}}, {"name": "binStepSeed", "docs": ["Bin step signer seed"], "type": {"array": ["u8", 2]}}, {"name": "pairType", "docs": ["Type of the pair"], "type": "u8"}, {"name": "activeId", "docs": ["Active bin id"], "type": "i32"}, {"name": "binStep", "docs": ["Bin step. Represent the price increment / decrement."], "type": "u16"}, {"name": "status", "docs": ["Status of the pair. Check PairStatus enum."], "type": "u8"}, {"name": "requireBaseFactorSeed", "docs": ["Require base factor seed"], "type": "u8"}, {"name": "baseFactorSeed", "docs": ["Base factor seed"], "type": {"array": ["u8", 2]}}, {"name": "activationType", "docs": ["Activation type"], "type": "u8"}, {"name": "creatorPoolOnOffControl", "docs": ["Allow pool creator to enable/disable pool with restricted validation. Only applicable for customizable permissionless pair type."], "type": "u8"}, {"name": "tokenXMint", "docs": ["Token X mint"], "type": "public<PERSON>ey"}, {"name": "tokenYMint", "docs": ["Token Y mint"], "type": "public<PERSON>ey"}, {"name": "reserveX", "docs": ["LB token X vault"], "type": "public<PERSON>ey"}, {"name": "reserveY", "docs": ["LB token Y vault"], "type": "public<PERSON>ey"}, {"name": "protocolFee", "docs": ["Uncollected protocol fee"], "type": {"defined": "ProtocolFee"}}, {"name": "padding1", "docs": ["_padding_1, previous Fee owner, BE CAREFUL FOR TOMBSTONE WHEN REUSE !!"], "type": {"array": ["u8", 32]}}, {"name": "rewardInfos", "docs": ["Farming reward information"], "type": {"array": [{"defined": "RewardInfo"}, 2]}}, {"name": "oracle", "docs": ["Oracle pubkey"], "type": "public<PERSON>ey"}, {"name": "binArrayBitmap", "docs": ["Packed initialized bin array state"], "type": {"array": ["u64", 16]}}, {"name": "lastUpdatedAt", "docs": ["Last time the pool fee parameter was updated"], "type": "i64"}, {"name": "padding2", "docs": ["_padding_2, previous whitelisted_wallet, BE CAREFUL FOR TOMBSTONE WHEN REUSE !!"], "type": {"array": ["u8", 32]}}, {"name": "preActivationSwapAddress", "docs": ["Address allowed to swap when the current point is greater than or equal to the pre-activation point. The pre-activation point is calculated as `activation_point - pre_activation_duration`."], "type": "public<PERSON>ey"}, {"name": "baseKey", "docs": ["Base keypair. Only required for permission pair"], "type": "public<PERSON>ey"}, {"name": "activationPoint", "docs": ["Time point to enable the pair. Only applicable for permission pair."], "type": "u64"}, {"name": "preActivationDuration", "docs": ["Duration before activation activation_point. Used to calculate pre-activation time point for pre_activation_swap_address"], "type": "u64"}, {"name": "padding3", "docs": ["_padding 3 is reclaimed free space from swap_cap_deactivate_point and swap_cap_amount before, BE CAREFUL FOR TOMBSTONE WHEN REUSE !!"], "type": {"array": ["u8", 8]}}, {"name": "padding4", "docs": ["_padding_4, previous lock_duration, BE CAREFUL FOR TOMBSTONE WHEN REUSE !!"], "type": "u64"}, {"name": "creator", "docs": ["Pool creator"], "type": "public<PERSON>ey"}, {"name": "tokenMintXProgramFlag", "docs": ["token_mint_x_program_flag"], "type": "u8"}, {"name": "tokenMintYProgramFlag", "docs": ["token_mint_y_program_flag"], "type": "u8"}, {"name": "reserved", "docs": ["Reserved space for future use"], "type": {"array": ["u8", 22]}}]}}, {"name": "Oracle", "type": {"kind": "struct", "fields": [{"name": "idx", "docs": ["Index of latest observation"], "type": "u64"}, {"name": "activeSize", "docs": ["Size of active sample. Active sample is initialized observation."], "type": "u64"}, {"name": "length", "docs": ["Number of observations"], "type": "u64"}]}}, {"name": "Position", "type": {"kind": "struct", "fields": [{"name": "lbPair", "docs": ["The LB pair of this position"], "type": "public<PERSON>ey"}, {"name": "owner", "docs": ["Owner of the position. Client rely on this to to fetch their positions."], "type": "public<PERSON>ey"}, {"name": "liquidityShares", "docs": ["Liquidity shares of this position in bins (lower_bin_id <-> upper_bin_id). This is the same as LP concept."], "type": {"array": ["u64", 70]}}, {"name": "rewardInfos", "docs": ["Farming reward information"], "type": {"array": [{"defined": "UserRewardInfo"}, 70]}}, {"name": "feeInfos", "docs": ["Swap fee to claim information"], "type": {"array": [{"defined": "FeeInfo"}, 70]}}, {"name": "lowerBinId", "docs": ["Lower bin ID"], "type": "i32"}, {"name": "upperBinId", "docs": ["Upper bin ID"], "type": "i32"}, {"name": "lastUpdatedAt", "docs": ["Last updated timestamp"], "type": "i64"}, {"name": "totalClaimedFeeXAmount", "docs": ["Total claimed token fee X"], "type": "u64"}, {"name": "totalClaimedFeeYAmount", "docs": ["Total claimed token fee Y"], "type": "u64"}, {"name": "totalClaimedRewards", "docs": ["Total claimed rewards"], "type": {"array": ["u64", 2]}}, {"name": "reserved", "docs": ["Reserved space for future use"], "type": {"array": ["u8", 160]}}]}}, {"name": "PositionV2", "type": {"kind": "struct", "fields": [{"name": "lbPair", "docs": ["The LB pair of this position"], "type": "public<PERSON>ey"}, {"name": "owner", "docs": ["Owner of the position. Client rely on this to to fetch their positions."], "type": "public<PERSON>ey"}, {"name": "liquidityShares", "docs": ["Liquidity shares of this position in bins (lower_bin_id <-> upper_bin_id). This is the same as LP concept."], "type": {"array": ["u128", 70]}}, {"name": "rewardInfos", "docs": ["Farming reward information"], "type": {"array": [{"defined": "UserRewardInfo"}, 70]}}, {"name": "feeInfos", "docs": ["Swap fee to claim information"], "type": {"array": [{"defined": "FeeInfo"}, 70]}}, {"name": "lowerBinId", "docs": ["Lower bin ID"], "type": "i32"}, {"name": "upperBinId", "docs": ["Upper bin ID"], "type": "i32"}, {"name": "lastUpdatedAt", "docs": ["Last updated timestamp"], "type": "i64"}, {"name": "totalClaimedFeeXAmount", "docs": ["Total claimed token fee X"], "type": "u64"}, {"name": "totalClaimedFeeYAmount", "docs": ["Total claimed token fee Y"], "type": "u64"}, {"name": "totalClaimedRewards", "docs": ["Total claimed rewards"], "type": {"array": ["u64", 2]}}, {"name": "operator", "docs": ["Operator of position"], "type": "public<PERSON>ey"}, {"name": "lockReleasePoint", "docs": ["Time point which the locked liquidity can be withdraw"], "type": "u64"}, {"name": "padding0", "docs": ["_padding_0, previous subjected_to_bootstrap_liquidity_locking, BE CAREFUL FOR TOMBSTONE WHEN REUSE !!"], "type": "u8"}, {"name": "feeOwner", "docs": ["Address is able to claim fee in this position, only valid for bootstrap_liquidity_position"], "type": "public<PERSON>ey"}, {"name": "reserved", "docs": ["Reserved space for future use"], "type": {"array": ["u8", 87]}}]}}, {"name": "PresetParameter2", "type": {"kind": "struct", "fields": [{"name": "binStep", "docs": ["Bin step. Represent the price increment / decrement."], "type": "u16"}, {"name": "baseFactor", "docs": ["Used for base fee calculation. base_fee_rate = base_factor * bin_step * 10 * 10^base_fee_power_factor"], "type": "u16"}, {"name": "filterPeriod", "docs": ["Filter period determine high frequency trading time window."], "type": "u16"}, {"name": "decayPeriod", "docs": ["Decay period determine when the volatile fee start decay / decrease."], "type": "u16"}, {"name": "variableFeeControl", "docs": ["Used to scale the variable fee component depending on the dynamic of the market"], "type": "u32"}, {"name": "maxVolatilityAccumulator", "docs": ["Maximum number of bin crossed can be accumulated. Used to cap volatile fee rate."], "type": "u32"}, {"name": "reductionFactor", "docs": ["Reduction factor controls the volatile fee rate decrement rate."], "type": "u16"}, {"name": "protocolShare", "docs": ["Portion of swap fees retained by the protocol by controlling protocol_share parameter. protocol_swap_fee = protocol_share * total_swap_fee"], "type": "u16"}, {"name": "index", "docs": ["index"], "type": "u16"}, {"name": "baseFeePowerFactor", "docs": ["Base fee power factor"], "type": "u8"}, {"name": "padding0", "docs": ["Padding 0 for future use"], "type": "u8"}, {"name": "padding1", "docs": ["Padding 1 for future use"], "type": {"array": ["u64", 20]}}]}}, {"name": "PresetParameter", "type": {"kind": "struct", "fields": [{"name": "binStep", "docs": ["Bin step. Represent the price increment / decrement."], "type": "u16"}, {"name": "baseFactor", "docs": ["Used for base fee calculation. base_fee_rate = base_factor * bin_step * 10 * 10^base_fee_power_factor"], "type": "u16"}, {"name": "filterPeriod", "docs": ["Filter period determine high frequency trading time window."], "type": "u16"}, {"name": "decayPeriod", "docs": ["Decay period determine when the volatile fee start decay / decrease."], "type": "u16"}, {"name": "reductionFactor", "docs": ["Reduction factor controls the volatile fee rate decrement rate."], "type": "u16"}, {"name": "variableFeeControl", "docs": ["Used to scale the variable fee component depending on the dynamic of the market"], "type": "u32"}, {"name": "maxVolatilityAccumulator", "docs": ["Maximum number of bin crossed can be accumulated. Used to cap volatile fee rate."], "type": "u32"}, {"name": "minBinId", "docs": ["Min bin id supported by the pool based on the configured bin step."], "type": "i32"}, {"name": "maxBinId", "docs": ["Max bin id supported by the pool based on the configured bin step."], "type": "i32"}, {"name": "protocolShare", "docs": ["Portion of swap fees retained by the protocol by controlling protocol_share parameter. protocol_swap_fee = protocol_share * total_swap_fee"], "type": "u16"}]}}, {"name": "TokenBadge", "docs": ["Parameter that set by the protocol"], "type": {"kind": "struct", "fields": [{"name": "tokenMint", "docs": ["token mint"], "type": "public<PERSON>ey"}, {"name": "padding", "docs": ["Reserve"], "type": {"array": ["u8", 128]}}]}}], "types": [{"name": "InitPresetParameters2Ix", "type": {"kind": "struct", "fields": [{"name": "index", "type": "u16"}, {"name": "binStep", "docs": ["Bin step. Represent the price increment / decrement."], "type": "u16"}, {"name": "baseFactor", "docs": ["Used for base fee calculation. base_fee_rate = base_factor * bin_step * 10 * 10^base_fee_power_factor"], "type": "u16"}, {"name": "filterPeriod", "docs": ["Filter period determine high frequency trading time window."], "type": "u16"}, {"name": "decayPeriod", "docs": ["Decay period determine when the volatile fee start decay / decrease."], "type": "u16"}, {"name": "reductionFactor", "docs": ["Reduction factor controls the volatile fee rate decrement rate."], "type": "u16"}, {"name": "variableFeeControl", "docs": ["Used to scale the variable fee component depending on the dynamic of the market"], "type": "u32"}, {"name": "maxVolatilityAccumulator", "docs": ["Maximum number of bin crossed can be accumulated. Used to cap volatile fee rate."], "type": "u32"}, {"name": "protocolShare", "docs": ["Portion of swap fees retained by the protocol by controlling protocol_share parameter. protocol_swap_fee = protocol_share * total_swap_fee"], "type": "u16"}, {"name": "baseFeePowerFactor", "docs": ["Base fee power factor"], "type": "u8"}]}}, {"name": "InitPresetParametersIx", "type": {"kind": "struct", "fields": [{"name": "binStep", "docs": ["Bin step. Represent the price increment / decrement."], "type": "u16"}, {"name": "baseFactor", "docs": ["Used for base fee calculation. base_fee_rate = base_factor * bin_step * 10 * 10^base_fee_power_factor"], "type": "u16"}, {"name": "filterPeriod", "docs": ["Filter period determine high frequency trading time window."], "type": "u16"}, {"name": "decayPeriod", "docs": ["Decay period determine when the volatile fee start decay / decrease."], "type": "u16"}, {"name": "reductionFactor", "docs": ["Reduction factor controls the volatile fee rate decrement rate."], "type": "u16"}, {"name": "variableFeeControl", "docs": ["Used to scale the variable fee component depending on the dynamic of the market"], "type": "u32"}, {"name": "maxVolatilityAccumulator", "docs": ["Maximum number of bin crossed can be accumulated. Used to cap volatile fee rate."], "type": "u32"}, {"name": "protocolShare", "docs": ["Portion of swap fees retained by the protocol by controlling protocol_share parameter. protocol_swap_fee = protocol_share * total_swap_fee"], "type": "u16"}]}}, {"name": "BaseFeeParameter", "type": {"kind": "struct", "fields": [{"name": "protocolShare", "docs": ["Portion of swap fees retained by the protocol by controlling protocol_share parameter. protocol_swap_fee = protocol_share * total_swap_fee"], "type": "u16"}, {"name": "baseFactor", "docs": ["Base factor for base fee rate"], "type": "u16"}, {"name": "baseFeePowerFactor", "docs": ["Base fee power factor"], "type": "u8"}]}}, {"name": "DynamicFeeParameter", "type": {"kind": "struct", "fields": [{"name": "filterPeriod", "docs": ["Filter period determine high frequency trading time window."], "type": "u16"}, {"name": "decayPeriod", "docs": ["Decay period determine when the volatile fee start decay / decrease."], "type": "u16"}, {"name": "reductionFactor", "docs": ["Reduction factor controls the volatile fee rate decrement rate."], "type": "u16"}, {"name": "variableFeeControl", "docs": ["Used to scale the variable fee component depending on the dynamic of the market"], "type": "u32"}, {"name": "maxVolatilityAccumulator", "docs": ["Maximum number of bin crossed can be accumulated. Used to cap volatile fee rate."], "type": "u32"}]}}, {"name": "LiquidityParameterByStrategyOneSide", "type": {"kind": "struct", "fields": [{"name": "amount", "docs": ["Amount of X token or Y token to deposit"], "type": "u64"}, {"name": "activeId", "docs": ["Active bin that integrator observe off-chain"], "type": "i32"}, {"name": "maxActiveBinSlippage", "docs": ["max active bin slippage allowed"], "type": "i32"}, {"name": "strategyParameters", "docs": ["strategy parameters"], "type": {"defined": "StrategyParameters"}}]}}, {"name": "LiquidityParameterByStrategy", "type": {"kind": "struct", "fields": [{"name": "amountX", "docs": ["Amount of X token to deposit"], "type": "u64"}, {"name": "amountY", "docs": ["Amount of Y token to deposit"], "type": "u64"}, {"name": "activeId", "docs": ["Active bin that integrator observe off-chain"], "type": "i32"}, {"name": "maxActiveBinSlippage", "docs": ["max active bin slippage allowed"], "type": "i32"}, {"name": "strategyParameters", "docs": ["strategy parameters"], "type": {"defined": "StrategyParameters"}}]}}, {"name": "StrategyParameters", "type": {"kind": "struct", "fields": [{"name": "minBinId", "docs": ["min bin id"], "type": "i32"}, {"name": "maxBinId", "docs": ["max bin id"], "type": "i32"}, {"name": "strategyType", "docs": ["strategy type"], "type": {"defined": "StrategyType"}}, {"name": "parameteres", "docs": ["parameters"], "type": {"array": ["u8", 64]}}]}}, {"name": "LiquidityOneSideParameter", "type": {"kind": "struct", "fields": [{"name": "amount", "docs": ["Amount of X token or Y token to deposit"], "type": "u64"}, {"name": "activeId", "docs": ["Active bin that integrator observe off-chain"], "type": "i32"}, {"name": "maxActiveBinSlippage", "docs": ["max active bin slippage allowed"], "type": "i32"}, {"name": "binLiquidityDist", "docs": ["Liquidity distribution to each bins"], "type": {"vec": {"defined": "BinLiquidityDistributionByWeight"}}}]}}, {"name": "BinLiquidityDistributionByWeight", "type": {"kind": "struct", "fields": [{"name": "binId", "docs": ["Define the bin ID wish to deposit to."], "type": "i32"}, {"name": "weight", "docs": ["weight of liquidity distributed for this bin id"], "type": "u16"}]}}, {"name": "LiquidityParameterByWeight", "type": {"kind": "struct", "fields": [{"name": "amountX", "docs": ["Amount of X token to deposit"], "type": "u64"}, {"name": "amountY", "docs": ["Amount of Y token to deposit"], "type": "u64"}, {"name": "activeId", "docs": ["Active bin that integrator observe off-chain"], "type": "i32"}, {"name": "maxActiveBinSlippage", "docs": ["max active bin slippage allowed"], "type": "i32"}, {"name": "binLiquidityDist", "docs": ["Liquidity distribution to each bins"], "type": {"vec": {"defined": "BinLiquidityDistributionByWeight"}}}]}}, {"name": "AddLiquiditySingleSidePreciseParameter", "type": {"kind": "struct", "fields": [{"name": "bins", "type": {"vec": {"defined": "CompressedBinDepositAmount"}}}, {"name": "decompressMultiplier", "type": "u64"}]}}, {"name": "CompressedBinDepositAmount", "type": {"kind": "struct", "fields": [{"name": "binId", "type": "i32"}, {"name": "amount", "type": "u32"}]}}, {"name": "BinLiquidityDistribution", "type": {"kind": "struct", "fields": [{"name": "binId", "docs": ["Define the bin ID wish to deposit to."], "type": "i32"}, {"name": "distributionX", "docs": ["DistributionX (or distributionY) is the percentages of amountX (or amountY) you want to add to each bin."], "type": "u16"}, {"name": "distributionY", "docs": ["DistributionX (or distributionY) is the percentages of amountX (or amountY) you want to add to each bin."], "type": "u16"}]}}, {"name": "LiquidityParameter", "type": {"kind": "struct", "fields": [{"name": "amountX", "docs": ["Amount of X token to deposit"], "type": "u64"}, {"name": "amountY", "docs": ["Amount of Y token to deposit"], "type": "u64"}, {"name": "binLiquidityDist", "docs": ["Liquidity distribution to each bins"], "type": {"vec": {"defined": "BinLiquidityDistribution"}}}]}}, {"name": "CustomizableParams", "type": {"kind": "struct", "fields": [{"name": "activeId", "docs": ["Pool price"], "type": "i32"}, {"name": "binStep", "docs": ["Bin step"], "type": "u16"}, {"name": "baseFactor", "docs": ["Base factor"], "type": "u16"}, {"name": "activationType", "docs": ["Activation type. 0 = Slot, 1 = Time. Check ActivationType enum"], "type": "u8"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "docs": ["Whether the pool has an alpha vault"], "type": "bool"}, {"name": "activationPoint", "docs": ["Decide when does the pool start trade. None = Now"], "type": {"option": "u64"}}, {"name": "creatorPoolOnOffControl", "docs": ["Pool creator have permission to enable/disable pool with restricted program validation. Only applicable for customizable permissionless pool."], "type": "bool"}, {"name": "baseFeePowerFactor", "docs": ["Base fee power factor"], "type": "u8"}, {"name": "padding", "docs": ["Padding, for future use"], "type": {"array": ["u8", 62]}}]}}, {"name": "InitPermissionPairIx", "type": {"kind": "struct", "fields": [{"name": "activeId", "type": "i32"}, {"name": "binStep", "type": "u16"}, {"name": "baseFactor", "type": "u16"}, {"name": "baseFeePowerFactor", "type": "u8"}, {"name": "activationType", "type": "u8"}, {"name": "protocolShare", "type": "u16"}]}}, {"name": "AddLiquiditySingleSidePreciseParameter2", "type": {"kind": "struct", "fields": [{"name": "bins", "type": {"vec": {"defined": "CompressedBinDepositAmount"}}}, {"name": "decompressMultiplier", "type": "u64"}, {"name": "maxAmount", "type": "u64"}]}}, {"name": "CompressedBinDepositAmount2", "type": {"kind": "struct", "fields": [{"name": "binId", "type": "i32"}, {"name": "amount", "type": "u32"}]}}, {"name": "InitializeLbPair2Params", "type": {"kind": "struct", "fields": [{"name": "activeId", "docs": ["Pool price"], "type": "i32"}, {"name": "padding", "docs": ["Padding, for future use"], "type": {"array": ["u8", 96]}}]}}, {"name": "BinLiquidityReduction", "type": {"kind": "struct", "fields": [{"name": "binId", "type": "i32"}, {"name": "bpsToRemove", "type": "u16"}]}}, {"name": "Bin", "type": {"kind": "struct", "fields": [{"name": "amountX", "docs": ["Amount of token X in the bin. This already excluded protocol fees."], "type": "u64"}, {"name": "amountY", "docs": ["Amount of token Y in the bin. This already excluded protocol fees."], "type": "u64"}, {"name": "price", "docs": ["Bin price"], "type": "u128"}, {"name": "liquiditySupply", "docs": ["Liquidities of the bin. This is the same as LP mint supply. q-number"], "type": "u128"}, {"name": "rewardPerTokenStored", "docs": ["reward_a_per_token_stored"], "type": {"array": ["u128", 2]}}, {"name": "feeAmountXPerTokenStored", "docs": ["Swap fee amount of token X per liquidity deposited."], "type": "u128"}, {"name": "feeAmountYPerTokenStored", "docs": ["Swap fee amount of token Y per liquidity deposited."], "type": "u128"}, {"name": "amountXIn", "docs": ["Total token X swap into the bin. Only used for tracking purpose."], "type": "u128"}, {"name": "amountYIn", "docs": ["Total token Y swap into he bin. Only used for tracking purpose."], "type": "u128"}]}}, {"name": "ProtocolFee", "type": {"kind": "struct", "fields": [{"name": "amountX", "type": "u64"}, {"name": "amountY", "type": "u64"}]}}, {"name": "RewardInfo", "docs": ["Stores the state relevant for tracking liquidity mining rewards"], "type": {"kind": "struct", "fields": [{"name": "mint", "docs": ["Reward token mint."], "type": "public<PERSON>ey"}, {"name": "vault", "docs": ["Reward vault token account."], "type": "public<PERSON>ey"}, {"name": "funder", "docs": ["Authority account that allows to fund rewards"], "type": "public<PERSON>ey"}, {"name": "rewardDuration", "docs": ["TODO check whether we need to store it in pool"], "type": "u64"}, {"name": "rewardDurationEnd", "docs": ["TODO check whether we need to store it in pool"], "type": "u64"}, {"name": "rewardRate", "docs": ["TODO check whether we need to store it in pool"], "type": "u128"}, {"name": "lastUpdateTime", "docs": ["The last time reward states were updated."], "type": "u64"}, {"name": "cumulativeSecondsWithEmptyLiquidityReward", "docs": ["Accumulated seconds where when farm distribute rewards, but the bin is empty. The reward will be accumulated for next reward time window."], "type": "u64"}]}}, {"name": "Observation", "type": {"kind": "struct", "fields": [{"name": "cumulativeActiveBinId", "docs": ["Cumulative active bin ID"], "type": "i128"}, {"name": "createdAt", "docs": ["Observation sample created timestamp"], "type": "i64"}, {"name": "lastUpdatedAt", "docs": ["Observation sample last updated timestamp"], "type": "i64"}]}}, {"name": "StaticParameters", "docs": ["Parameter that set by the protocol"], "type": {"kind": "struct", "fields": [{"name": "baseFactor", "docs": ["Used for base fee calculation. base_fee_rate = base_factor * bin_step * 10 * 10^base_fee_power_factor"], "type": "u16"}, {"name": "filterPeriod", "docs": ["Filter period determine high frequency trading time window."], "type": "u16"}, {"name": "decayPeriod", "docs": ["Decay period determine when the volatile fee start decay / decrease."], "type": "u16"}, {"name": "reductionFactor", "docs": ["Reduction factor controls the volatile fee rate decrement rate."], "type": "u16"}, {"name": "variableFeeControl", "docs": ["Used to scale the variable fee component depending on the dynamic of the market"], "type": "u32"}, {"name": "maxVolatilityAccumulator", "docs": ["Maximum number of bin crossed can be accumulated. Used to cap volatile fee rate."], "type": "u32"}, {"name": "minBinId", "docs": ["Min bin id supported by the pool based on the configured bin step."], "type": "i32"}, {"name": "maxBinId", "docs": ["Max bin id supported by the pool based on the configured bin step."], "type": "i32"}, {"name": "protocolShare", "docs": ["Portion of swap fees retained by the protocol by controlling protocol_share parameter. protocol_swap_fee = protocol_share * total_swap_fee"], "type": "u16"}, {"name": "baseFeePowerFactor", "docs": ["Base fee power factor"], "type": "u8"}, {"name": "padding", "docs": ["Padding for bytemuck safe alignment"], "type": {"array": ["u8", 5]}}]}}, {"name": "VariableParameters", "docs": ["Parameters that changes based on dynamic of the market"], "type": {"kind": "struct", "fields": [{"name": "volatilityAccumulator", "docs": ["Volatility accumulator measure the number of bin crossed since reference bin ID. Normally (without filter period taken into consideration), reference bin ID is the active bin of last swap.", "It affects the variable fee rate"], "type": "u32"}, {"name": "volatilityReference", "docs": ["Volatility reference is decayed volatility accumulator. It is always <= volatility_accumulator"], "type": "u32"}, {"name": "indexReference", "docs": ["Active bin id of last swap."], "type": "i32"}, {"name": "padding", "docs": ["Padding for bytemuck safe alignment"], "type": {"array": ["u8", 4]}}, {"name": "lastUpdateTimestamp", "docs": ["Last timestamp the variable parameters was updated"], "type": "i64"}, {"name": "padding1", "docs": ["Padding for bytemuck safe alignment"], "type": {"array": ["u8", 8]}}]}}, {"name": "FeeInfo", "type": {"kind": "struct", "fields": [{"name": "feeXPerTokenComplete", "type": "u128"}, {"name": "feeYPerTokenComplete", "type": "u128"}, {"name": "feeXPending", "type": "u64"}, {"name": "feeYPending", "type": "u64"}]}}, {"name": "UserRewardInfo", "type": {"kind": "struct", "fields": [{"name": "rewardPerTokenCompletes", "type": {"array": ["u128", 2]}}, {"name": "rewardPendings", "type": {"array": ["u64", 2]}}]}}, {"name": "RemainingAccountsSlice", "type": {"kind": "struct", "fields": [{"name": "accountsType", "type": {"defined": "AccountsType"}}, {"name": "length", "type": "u8"}]}}, {"name": "RemainingAccountsInfo", "type": {"kind": "struct", "fields": [{"name": "slices", "type": {"vec": {"defined": "RemainingAccountsSlice"}}}]}}, {"name": "StrategyType", "type": {"kind": "enum", "variants": [{"name": "SpotOneSide"}, {"name": "CurveOneSide"}, {"name": "BidAskOneSide"}, {"name": "SpotBalanced"}, {"name": "CurveBalanced"}, {"name": "BidAskBalanced"}, {"name": "SpotImBalanced"}, {"name": "CurveImBalanced"}, {"name": "BidAskImBalanced"}]}}, {"name": "Rounding", "type": {"kind": "enum", "variants": [{"name": "Up"}, {"name": "Down"}]}}, {"name": "ActivationType", "docs": ["Type of the activation"], "type": {"kind": "enum", "variants": [{"name": "Slot"}, {"name": "Timestamp"}]}}, {"name": "LayoutVersion", "docs": ["Layout version"], "type": {"kind": "enum", "variants": [{"name": "V0"}, {"name": "V1"}]}}, {"name": "PairType", "docs": ["Type of the Pair. 0 = Permissionless, 1 = Permission, 2 = CustomizablePermissionless. Putting 0 as permissionless for backward compatibility."], "type": {"kind": "enum", "variants": [{"name": "Permissionless"}, {"name": "Permission"}, {"name": "CustomizablePermissionless"}, {"name": "PermissionlessV2"}]}}, {"name": "PairStatus", "docs": ["Pair status. 0 = Enabled, 1 = Disabled. Putting 0 as enabled for backward compatibility."], "type": {"kind": "enum", "variants": [{"name": "Enabled"}, {"name": "Disabled"}]}}, {"name": "TokenProgramFlags", "type": {"kind": "enum", "variants": [{"name": "TokenProgram"}, {"name": "TokenProgram2022"}]}}, {"name": "AccountsType", "type": {"kind": "enum", "variants": [{"name": "TransferHookX"}, {"name": "TransferHookY"}, {"name": "TransferHookReward"}]}}], "events": [{"name": "CompositionFee", "fields": [{"name": "from", "type": "public<PERSON>ey", "index": false}, {"name": "binId", "type": "i16", "index": false}, {"name": "tokenXFeeAmount", "type": "u64", "index": false}, {"name": "tokenYFeeAmount", "type": "u64", "index": false}, {"name": "protocolTokenXFeeAmount", "type": "u64", "index": false}, {"name": "protocolTokenYFeeAmount", "type": "u64", "index": false}]}, {"name": "AddLiquidity", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "from", "type": "public<PERSON>ey", "index": false}, {"name": "position", "type": "public<PERSON>ey", "index": false}, {"name": "amounts", "type": {"array": ["u64", 2]}, "index": false}, {"name": "activeBinId", "type": "i32", "index": false}]}, {"name": "RemoveLiquidity", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "from", "type": "public<PERSON>ey", "index": false}, {"name": "position", "type": "public<PERSON>ey", "index": false}, {"name": "amounts", "type": {"array": ["u64", 2]}, "index": false}, {"name": "activeBinId", "type": "i32", "index": false}]}, {"name": "<PERSON><PERSON><PERSON>", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "from", "type": "public<PERSON>ey", "index": false}, {"name": "startBinId", "type": "i32", "index": false}, {"name": "endBinId", "type": "i32", "index": false}, {"name": "amountIn", "type": "u64", "index": false}, {"name": "amountOut", "type": "u64", "index": false}, {"name": "swapForY", "type": "bool", "index": false}, {"name": "fee", "type": "u64", "index": false}, {"name": "protocolFee", "type": "u64", "index": false}, {"name": "feeBps", "type": "u128", "index": false}, {"name": "host<PERSON>ee", "type": "u64", "index": false}]}, {"name": "<PERSON>laim<PERSON>eward", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "position", "type": "public<PERSON>ey", "index": false}, {"name": "owner", "type": "public<PERSON>ey", "index": false}, {"name": "rewardIndex", "type": "u64", "index": false}, {"name": "totalReward", "type": "u64", "index": false}]}, {"name": "FundReward", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "funder", "type": "public<PERSON>ey", "index": false}, {"name": "rewardIndex", "type": "u64", "index": false}, {"name": "amount", "type": "u64", "index": false}]}, {"name": "InitializeReward", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "rewardMint", "type": "public<PERSON>ey", "index": false}, {"name": "funder", "type": "public<PERSON>ey", "index": false}, {"name": "rewardIndex", "type": "u64", "index": false}, {"name": "rewardDuration", "type": "u64", "index": false}]}, {"name": "UpdateRewardDuration", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "rewardIndex", "type": "u64", "index": false}, {"name": "oldRewardDuration", "type": "u64", "index": false}, {"name": "newRewardDuration", "type": "u64", "index": false}]}, {"name": "UpdateRewardFunder", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "rewardIndex", "type": "u64", "index": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "public<PERSON>ey", "index": false}, {"name": "new<PERSON><PERSON><PERSON>", "type": "public<PERSON>ey", "index": false}]}, {"name": "PositionClose", "fields": [{"name": "position", "type": "public<PERSON>ey", "index": false}, {"name": "owner", "type": "public<PERSON>ey", "index": false}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "position", "type": "public<PERSON>ey", "index": false}, {"name": "owner", "type": "public<PERSON>ey", "index": false}, {"name": "feeX", "type": "u64", "index": false}, {"name": "feeY", "type": "u64", "index": false}]}, {"name": "LbPairCreate", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "binStep", "type": "u16", "index": false}, {"name": "tokenX", "type": "public<PERSON>ey", "index": false}, {"name": "tokenY", "type": "public<PERSON>ey", "index": false}]}, {"name": "PositionCreate", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "position", "type": "public<PERSON>ey", "index": false}, {"name": "owner", "type": "public<PERSON>ey", "index": false}]}, {"name": "IncreasePositionLength", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "position", "type": "public<PERSON>ey", "index": false}, {"name": "owner", "type": "public<PERSON>ey", "index": false}, {"name": "lengthToAdd", "type": "u16", "index": false}, {"name": "side", "type": "u8", "index": false}]}, {"name": "DecreasePositionLength", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "position", "type": "public<PERSON>ey", "index": false}, {"name": "owner", "type": "public<PERSON>ey", "index": false}, {"name": "lengthToRemove", "type": "u16", "index": false}, {"name": "side", "type": "u8", "index": false}]}, {"name": "FeeParameterUpdate", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "protocolShare", "type": "u16", "index": false}, {"name": "baseFactor", "type": "u16", "index": false}]}, {"name": "DynamicFeeParameterUpdate", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "filterPeriod", "type": "u16", "index": false}, {"name": "decayPeriod", "type": "u16", "index": false}, {"name": "reductionFactor", "type": "u16", "index": false}, {"name": "variableFeeControl", "type": "u32", "index": false}, {"name": "maxVolatilityAccumulator", "type": "u32", "index": false}]}, {"name": "IncreaseObservation", "fields": [{"name": "oracle", "type": "public<PERSON>ey", "index": false}, {"name": "newObservationLength", "type": "u64", "index": false}]}, {"name": "WithdrawIneligibleReward", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "rewardMint", "type": "public<PERSON>ey", "index": false}, {"name": "amount", "type": "u64", "index": false}]}, {"name": "UpdatePositionOperator", "fields": [{"name": "position", "type": "public<PERSON>ey", "index": false}, {"name": "oldOperator", "type": "public<PERSON>ey", "index": false}, {"name": "newOperator", "type": "public<PERSON>ey", "index": false}]}, {"name": "UpdatePositionLockReleasePoint", "fields": [{"name": "position", "type": "public<PERSON>ey", "index": false}, {"name": "currentPoint", "type": "u64", "index": false}, {"name": "newLockReleasePoint", "type": "u64", "index": false}, {"name": "oldLockReleasePoint", "type": "u64", "index": false}, {"name": "sender", "type": "public<PERSON>ey", "index": false}]}, {"name": "GoToABin", "fields": [{"name": "lbPair", "type": "public<PERSON>ey", "index": false}, {"name": "fromBinId", "type": "i32", "index": false}, {"name": "toBinId", "type": "i32", "index": false}]}], "errors": [{"code": 6000, "name": "InvalidStartBinIndex", "msg": "Invalid start bin index"}, {"code": 6001, "name": "InvalidBinId", "msg": "<PERSON><PERSON><PERSON> bin id"}, {"code": 6002, "name": "InvalidInput", "msg": "Invalid input data"}, {"code": 6003, "name": "ExceededAmountSlippageTolerance", "msg": "Exceeded amount slippage tolerance"}, {"code": 6004, "name": "ExceededBinSlippageTolerance", "msg": "Exceeded bin slippage tolerance"}, {"code": 6005, "name": "CompositionFactorFlawed", "msg": "Composition factor flawed"}, {"code": 6006, "name": "NonPresetBinStep", "msg": "Non preset bin step"}, {"code": 6007, "name": "ZeroLiquidity", "msg": "Zero liquidity"}, {"code": 6008, "name": "InvalidPosition", "msg": "Invalid position"}, {"code": 6009, "name": "BinArrayNotFound", "msg": "Bin array not found"}, {"code": 6010, "name": "InvalidTokenMint", "msg": "Invalid token mint"}, {"code": 6011, "name": "InvalidAccountForSingleDeposit", "msg": "Invalid account for single deposit"}, {"code": 6012, "name": "PairInsufficientLiquidity", "msg": "Pair insufficient liquidity"}, {"code": 6013, "name": "InvalidFeeOwner", "msg": "Invalid fee owner"}, {"code": 6014, "name": "InvalidFeeWithdrawAmount", "msg": "Invalid fee withdraw amount"}, {"code": 6015, "name": "InvalidAdmin", "msg": "Invalid admin"}, {"code": 6016, "name": "Identical<PERSON><PERSON><PERSON><PERSON><PERSON>", "msg": "Identical fee owner"}, {"code": 6017, "name": "InvalidBps", "msg": "Invalid basis point"}, {"code": 6018, "name": "MathOverflow", "msg": "Math operation overflow"}, {"code": 6019, "name": "TypeCastFailed", "msg": "Type cast error"}, {"code": 6020, "name": "InvalidRewardIndex", "msg": "Invalid reward index"}, {"code": 6021, "name": "InvalidRewardDuration", "msg": "Invalid reward duration"}, {"code": 6022, "name": "RewardInitialized", "msg": "<PERSON><PERSON> already initialized"}, {"code": 6023, "name": "RewardUninitialized", "msg": "Reward not initialized"}, {"code": 6024, "name": "IdenticalFunder", "msg": "Identical funder"}, {"code": 6025, "name": "RewardCampaignInProgress", "msg": "Reward campaign in progress"}, {"code": 6026, "name": "IdenticalRewardDuration", "msg": "Reward duration is the same"}, {"code": 6027, "name": "InvalidBinArray", "msg": "Invalid bin array"}, {"code": 6028, "name": "NonContinuousBinArrays", "msg": "Bin arrays must be continuous"}, {"code": 6029, "name": "InvalidRewardVault", "msg": "Invalid reward vault"}, {"code": 6030, "name": "NonEmptyPosition", "msg": "Position is not empty"}, {"code": 6031, "name": "UnauthorizedAccess", "msg": "Unauthorized access"}, {"code": 6032, "name": "InvalidFeeParameter", "msg": "Invalid fee parameter"}, {"code": 6033, "name": "<PERSON><PERSON><PERSON><PERSON>", "msg": "Missing oracle account"}, {"code": 6034, "name": "InsufficientSample", "msg": "Insufficient observation sample"}, {"code": 6035, "name": "InvalidLookupTimestamp", "msg": "Invalid lookup timestamp"}, {"code": 6036, "name": "BitmapExtensionAccountIsNotProvided", "msg": "Bitmap extension account is not provided"}, {"code": 6037, "name": "CannotFindNonZeroLiquidityBinArrayId", "msg": "Cannot find non-zero liquidity binArrayId"}, {"code": 6038, "name": "BinIdOutOfBound", "msg": "Bin id out of bound"}, {"code": 6039, "name": "InsufficientOutAmount", "msg": "Insufficient amount in for minimum out"}, {"code": 6040, "name": "InvalidPositionWidth", "msg": "Invalid position width"}, {"code": 6041, "name": "ExcessiveFeeUpdate", "msg": "Excessive fee update"}, {"code": 6042, "name": "PoolDisabled", "msg": "Pool disabled"}, {"code": 6043, "name": "InvalidPoolType", "msg": "Invalid pool type"}, {"code": 6044, "name": "ExceedMaxWhitelist", "msg": "Whitelist for wallet is full"}, {"code": 6045, "name": "InvalidIndex", "msg": "Invalid index"}, {"code": 6046, "name": "RewardNotEnded", "msg": "<PERSON><PERSON> not ended"}, {"code": 6047, "name": "MustWithdrawnIneligibleReward", "msg": "Must withdraw ineligible reward"}, {"code": 6048, "name": "Unauthorized<PERSON>ddress", "msg": "Unauthorized address"}, {"code": 6049, "name": "OperatorsAreTheSame", "msg": "Cannot update because operators are the same"}, {"code": 6050, "name": "WithdrawToWrongTokenAccount", "msg": "Withdraw to wrong token account"}, {"code": 6051, "name": "WrongRentReceiver", "msg": "Wrong rent receiver"}, {"code": 6052, "name": "AlreadyPassActivationPoint", "msg": "Already activated"}, {"code": 6053, "name": "ExceedMaxSwappedAmount", "msg": "Swapped amount is exceeded max swapped amount"}, {"code": 6054, "name": "InvalidStrategyParameters", "msg": "Invalid strategy parameters"}, {"code": 6055, "name": "LiquidityLocked", "msg": "Liquidity locked"}, {"code": 6056, "name": "BinRangeIsNotEmpty", "msg": "Bin range is not empty"}, {"code": 6057, "name": "NotExactAmountOut", "msg": "Amount out is not matched with exact amount out"}, {"code": 6058, "name": "InvalidActivationType", "msg": "Invalid activation type"}, {"code": 6059, "name": "InvalidActivationDuration", "msg": "Invalid activation duration"}, {"code": 6060, "name": "MissingTokenAmountAsTokenLaunchProof", "msg": "Missing token amount as token launch owner proof"}, {"code": 6061, "name": "InvalidQuoteToken", "msg": "Quote token must be SOL or USDC"}, {"code": 6062, "name": "InvalidBinStep", "msg": "Invalid bin step"}, {"code": 6063, "name": "InvalidBaseFee", "msg": "Invalid base fee"}, {"code": 6064, "name": "InvalidPreActivationDuration", "msg": "Invalid pre-activation duration"}, {"code": 6065, "name": "AlreadyPassPreActivationSwapPoint", "msg": "Already pass pre-activation swap point"}, {"code": 6066, "name": "InvalidStatus", "msg": "Invalid status"}, {"code": 6067, "name": "ExceededMaxOracleLength", "msg": "Exceed max oracle length"}, {"code": 6068, "name": "InvalidMinimumLiquidity", "msg": "Invalid minimum liquidity"}, {"code": 6069, "name": "NotSupportMint", "msg": "Not support token_2022 mint extension"}, {"code": 6070, "name": "UnsupportedMintExtension", "msg": "Unsupported mint extension"}, {"code": 6071, "name": "UnsupportNativeMintToken2022", "msg": "Unsupported native mint token2022"}, {"code": 6072, "name": "UnmatchTokenMint", "msg": "Unmatch token mint"}, {"code": 6073, "name": "UnsupportedTokenMint", "msg": "Unsupported token mint"}, {"code": 6074, "name": "InsufficientRemainingAccounts", "msg": "Insufficient remaining accounts"}, {"code": 6075, "name": "InvalidRemainingAccountSlice", "msg": "Invalid remaining account slice"}, {"code": 6076, "name": "DuplicatedRemainingAccountTypes", "msg": "Duplicated remaining account types"}, {"code": 6077, "name": "MissingRemainingAccountForTransferHook", "msg": "Missing remaining account for transfer hook"}, {"code": 6078, "name": "NoTransferHookProgram", "msg": "Remaining account was passed for transfer hook but there's no hook program"}, {"code": 6079, "name": "ZeroFundedAmount", "msg": "Zero funded amount"}, {"code": 6080, "name": "InvalidSide", "msg": "Invalid side"}, {"code": 6081, "name": "InvalidResizeLength", "msg": "Invalid resize length"}, {"code": 6082, "name": "NotSupportAtTheMoment", "msg": "Not support at the moment"}]}