<a id="readme-top"></a>

# Streaming Meteora Pools transaction using gRPC and parsing instructions

This project enables real-time streaming and parsing of Meteora pools transactions using gRPC, ensuring efficient data extraction and structured serialization for seamless processing. By decoding transaction data in real time, it provides valuable insights while maintaining high performance and scalability. Built for Solana’s ecosystem, the implementation leverages Rust’s strong type safety and optimized concurrency to handle high-throughput transaction streams, making it a powerful solution for DeFi applications and blockchain analytics.

![screenshot](assets/meteora_pool_screenshot.png?raw=true "Screenshot")

## Getting Started

1. **Clone the repository:**
   ```bash
   git clone https://github.com/Shyft-to/solana-defi.git
   cd PumpFun/Typescript/stream_and_parse_meteora_pools_instructions
   ```

2. **Install Dependencies:**

    ```bash
    # For example, if using npm
    npm i
    ```

3. **Run the script:**

    ```bash
    # To run the script
    npm run start
    ```

*Note: Please open `.env` and input your env details before running the script.*

## Related Links

Shyft gRPC Docs: [https://docs.shyft.to/solana-fast-grpc/grpc-docs]