<a id="readme-top"></a>
# Stream and Parse Meteora Pools on Solana Using Shyft gRPC for Real-Time Account Updates

This project utilizes Shyft gRPC to stream Meteora pool accounts on the Solana blockchain,
providing real-time insights and data analysis. By leveraging gRPC, 
it enables seamless streaming of account updates to monitor pool activities.
The primary focus is on parsing and analyzing Meteora pools by identifying key account  and 
updates associated with liquidity pools, offering valuable information on pool status.

![screenshot](assets/meteora-screenshot.png?raw=true "Screenshot")

## Getting Started

1. **Clone the repository:**
   ```bash
   git clone https://github.com/Shyft-to/solana-defi.git
   cd Token/Typescript/stream_parse_meteora_account

2. **Install Dependencies:**
    # For example,
    ```bash 
    npm i or npm install

3. **Run the script:**
   
   ```bash
   # To run the script
   npm run start

 *Note: Please in `.env`, input your env details before running the script.*
<p align="right">(<a href="#readme-top">back to top</a>)</p>


## Related Links

_For more examples, please refer to the [Documentation](https://docs.shyft.to/solana-fast-grpc/grpc-docs)_, _or feel free to visit our [Website](https://shyft.to/)_, _to get a free API Key_.
<p align="right">(<a href="#readme-top">back to top</a>)</p>   