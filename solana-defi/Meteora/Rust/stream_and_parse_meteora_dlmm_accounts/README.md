# Streaming and parsing Meteora DLMM accounts using gRPC

This project leverages Solana's gRPC streaming capabilities to efficiently monitor Meteora DLMM account data. By utilizing the IDL for parsing, it enables real-time analysis and insights into DLMM activities on the Solana blockchain.

```
$ cargo run -- --endpoint <endpoint> --x-token <token>
```

![screenshot](assets/usage-screenshot.png?raw=true "Screenshot")

## Docs
Shyft Website: [https://shyft.to/#solana-grpc-streaming-service]  
Shyft gRPC Docs: [https://docs.shyft.to/solana-fast-grpc/grpc-docs]
