{"name": "shyft", "version": "1.0.0", "description": "", "main": "index.ts", "scripts": {"start": "npx ts-node index.ts", "build": "npx tsc"}, "author": "", "license": "ISC", "dependencies": {"@triton-one/yellowstone-grpc": "^0.4.0", "@solana/web3.js": "^1.90.0", "@project-serum/anchor": "^0.26.0", "@shyft-to/solana-transaction-parser": "^1.1.17", "@solana/buffer-layout": "^4.0.1", "@solana/buffer-layout-utils": "^0.2.0", "dotenv": "^16.4.5", "lodash": "^4.17.21", "node-telegram-bot-api": "^0.64.0", "bs58": "^5.0.0"}, "devDependencies": {"ts-node": "^10.9.2", "typescript": "^4.9.3"}}