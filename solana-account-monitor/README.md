# Solana账户监控工具

这个工具用于监控Solana账户的交易活动，并解析交易中的mint、对应的pool market信息以及地址查找表。

## 功能特点

- 监控指定Solana账户地址的交易活动
- 解析交易中的代币mint信息
- 查询代币相关的池子信息（使用Shyft API）
- 获取Jupiter路由信息
- 解析地址查找表数据
- 实时监控新交易

## 安装

1. 克隆仓库并安装依赖：

```bash
cd solana-account-monitor
npm install
```

2. 配置环境变量：

复制`.env.example`文件为`.env`，并根据需要修改配置：

```bash
cp .env.example .env
```

主要配置项：
- `RPC_ENDPOINT`: Solana RPC节点地址
- `MONITORED_ADDRESS`: 要监控的账户地址
- `HISTORY_LIMIT`: 历史交易获取数量限制
- `POLLING_INTERVAL`: 轮询间隔（毫秒）
- `SHYFT_API_KEY`: Shyft API密钥

## 使用方法

运行程序：

```bash
npx ts-node src/index.ts
```

程序将开始监控指定账户的交易活动，并在控制台输出解析结果。

## 输出信息

程序会输出以下信息：

1. 交易签名和时间戳
2. 地址查找表信息（如果有）
3. 交易中涉及的代币Mint地址
4. 每个Mint地址对应的池子信息：
   - 池地址
   - 池类型（如Raydium, Orca等）
   - 基础代币和报价代币
   - 价格和交易量
5. Jupiter路由信息（从USDC到目标代币）

## 扩展

可以根据需要扩展以下功能：

1. 添加更多DEX的API支持
2. 实现交易提醒（如Telegram, Discord等）
3. 添加数据存储功能
4. 构建Web界面展示监控结果

## 依赖库

- @solana/web3.js: Solana区块链交互
- @solana/spl-token: SPL代币程序交互
- axios: HTTP请求
- dotenv: 环境变量管理
