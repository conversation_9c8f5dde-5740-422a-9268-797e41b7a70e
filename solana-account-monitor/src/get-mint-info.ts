import { Connection, PublicKey } from '@solana/web3.js';
import { MintLayout, TOKEN_PROGRAM_ID } from '@solana/spl-token';
import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 配置
const config = {
  rpcEndpoint: process.env.RPC_ENDPOINT || 'http://************:8899',
  dataDir: process.env.DATA_DIR || '../data',
  meteoraProgramId: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo'
};

// 创建连接
const connection = new Connection(config.rpcEndpoint);

// 获取账户信息
async function getAccountInfo(address: string) {
  try {
    // 创建 PublicKey 对象
    const publicKey = new PublicKey(address);
    
    // 获取账户信息
    const accountInfo = await connection.getAccountInfo(publicKey);
    
    if (!accountInfo) {
      console.log(`未找到账户: ${address}`);
      return null;
    }
    
    // 返回账户信息
    return {
      address: address,
      owner: accountInfo.owner.toString(),
      data: accountInfo.data,
      executable: accountInfo.executable,
      lamports: accountInfo.lamports,
      rentEpoch: accountInfo.rentEpoch
    };
  } catch (error) {
    console.error(`获取账户信息时出错 ${address}:`, error);
    return null;
  }
}

// 获取 Mint 信息
async function getMintInfo(mintAddress: string) {
  try {
    // 创建 PublicKey 对象
    const mintPublicKey = new PublicKey(mintAddress);
    
    // 获取账户信息
    const accountInfo = await connection.getAccountInfo(mintPublicKey);
    
    if (!accountInfo) {
      console.log(`未找到账户: ${mintAddress}`);
      return null;
    }
    
    // 检查账户是否由 Token Program 所有
    if (!accountInfo.owner.equals(TOKEN_PROGRAM_ID)) {
      console.log(`该账户不是由 Token Program 所有: ${mintAddress}`);
      return null;
    }
    
    // 解析 Mint 数据
    const mintInfo = MintLayout.decode(accountInfo.data);
    
    // 获取 Mint Authority (Owner)
    const mintAuthority = mintInfo.mintAuthorityOption === 0 
      ? null 
      : new PublicKey(mintInfo.mintAuthority).toString();
    
    // 获取 Freeze Authority
    const freezeAuthority = mintInfo.freezeAuthorityOption === 0 
      ? null 
      : new PublicKey(mintInfo.freezeAuthority).toString();
    
    // 返回详细信息
    return {
      address: mintAddress,
      owner: accountInfo.owner.toString(),
      mintAuthority: mintAuthority,
      freezeAuthority: freezeAuthority,
      supply: mintInfo.supply.toString(),
      decimals: mintInfo.decimals,
      isInitialized: mintInfo.isInitialized,
      accountData: {
        space: accountInfo.data.length,
        executable: accountInfo.executable,
        rentEpoch: accountInfo.rentEpoch,
        lamports: accountInfo.lamports
      }
    };
  } catch (error) {
    console.error(`获取 Mint 信息时出错:`, error);
    return null;
  }
}

// 从交易数据中提取所有账户地址
function extractAccountsFromTransaction(transactionData: any): string[] {
  const accounts = new Set<string>();
  
  try {
    // 提取交易中的账户密钥
    if (transactionData.transaction && transactionData.transaction.message && transactionData.transaction.message.accountKeys) {
      transactionData.transaction.message.accountKeys.forEach((account: any) => {
        if (account.pubkey) {
          accounts.add(account.pubkey);
        }
      });
    }
    
    // 提取指令中的账户
    if (transactionData.transaction && transactionData.transaction.message && transactionData.transaction.message.instructions) {
      transactionData.transaction.message.instructions.forEach((instruction: any) => {
        if (instruction.accounts) {
          instruction.accounts.forEach((account: string) => {
            accounts.add(account);
          });
        }
      });
    }
    
    // 提取内部指令中的账户
    if (transactionData.meta && transactionData.meta.innerInstructions) {
      transactionData.meta.innerInstructions.forEach((innerIx: any) => {
        if (innerIx.instructions) {
          innerIx.instructions.forEach((instruction: any) => {
            if (instruction.accounts) {
              instruction.accounts.forEach((account: string) => {
                accounts.add(account);
              });
            }
          });
        }
      });
    }
    
    // 提取代币余额中的 mint 地址
    if (transactionData.meta && transactionData.meta.preTokenBalances) {
      transactionData.meta.preTokenBalances.forEach((balance: any) => {
        if (balance.mint) {
          accounts.add(balance.mint);
        }
      });
    }
    
    if (transactionData.meta && transactionData.meta.postTokenBalances) {
      transactionData.meta.postTokenBalances.forEach((balance: any) => {
        if (balance.mint) {
          accounts.add(balance.mint);
        }
      });
    }
    
    return Array.from(accounts);
  } catch (error) {
    console.error('提取账户时出错:', error);
    return [];
  }
}

// 处理单个交易文件
async function processTransactionFile(filePath: string) {
  try {
    console.log(`处理交易文件: ${filePath}`);
    
    // 读取交易数据
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const transactionData = JSON.parse(fileContent);
    
    // 提取所有账户
    const accounts = extractAccountsFromTransaction(transactionData);
    console.log(`找到 ${accounts.length} 个账户`);
    
    // 检查每个账户
    const meteoraAccounts = [];
    
    for (const account of accounts) {
      const accountInfo = await getAccountInfo(account);
      
      if (accountInfo && accountInfo.owner === config.meteoraProgramId) {
        meteoraAccounts.push({
          address: account,
          owner: accountInfo.owner,
          lamports: accountInfo.lamports
        });
        console.log(`找到 Meteora 账户: ${account}`);
      }
    }
    
    // 保存结果
    if (meteoraAccounts.length > 0) {
      const outputPath = path.join(config.dataDir, 'meteora-accounts.json');
      
      // 检查文件是否存在，如果存在则读取现有数据
      let existingData: any[] = [];
      if (fs.existsSync(outputPath)) {
        const existingContent = fs.readFileSync(outputPath, 'utf8');
        try {
          existingData = JSON.parse(existingContent);
        } catch (e) {
          console.error('解析现有数据时出错:', e);
        }
      }
      
      // 合并新数据，避免重复
      const existingAddresses = new Set(existingData.map(item => item.address));
      const newAccounts = meteoraAccounts.filter(account => !existingAddresses.has(account.address));
      
      if (newAccounts.length > 0) {
        const updatedData = [...existingData, ...newAccounts];
        fs.writeFileSync(outputPath, JSON.stringify(updatedData, null, 2));
        console.log(`保存了 ${newAccounts.length} 个新的 Meteora 账户到 ${outputPath}`);
      } else {
        console.log('没有发现新的 Meteora 账户');
      }
    }
    
    return meteoraAccounts;
  } catch (error) {
    console.error(`处理交易文件时出错 ${filePath}:`, error);
    return [];
  }
}

// 处理所有交易文件
async function processAllTransactionFiles() {
  const txDir = path.join(config.dataDir, 'transactions');
  
  if (!fs.existsSync(txDir)) {
    console.error(`交易目录不存在: ${txDir}`);
    return;
  }
  
  const files = fs.readdirSync(txDir).filter(file => file.endsWith('.json') && !file.includes('_summary'));
  console.log(`找到 ${files.length} 个交易文件`);
  
  for (const file of files) {
    const filePath = path.join(txDir, file);
    await processTransactionFile(filePath);
  }
  
  console.log('所有交易文件处理完成');
}

// 主函数
async function main() {
  try {
    console.log(`使用 RPC 端点: ${config.rpcEndpoint}`);
    console.log(`查找 owner 为: ${config.meteoraProgramId} 的账户`);
    
    await processAllTransactionFiles();
  } catch (error) {
    console.error('程序运行出错:', error);
  }
}

// 运行主函数
main().catch(error => {
  console.error('程序运行出错:', error);
  process.exit(1);
});