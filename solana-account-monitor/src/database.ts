import * as sqlite3 from 'sqlite3';
import { Database, open } from 'sqlite';
import * as path from 'path';

export class TransactionDatabase {
  private db: Database | null = null;
  private readonly dbPath: string;
  private readonly maxRecords: number = 10;

  constructor(dataDir: string) {
    this.dbPath = path.join(dataDir, 'transactions.db');
  }

  /**
   * 初始化数据库连接和表结构
   */
  async initialize(): Promise<void> {
    try {
      this.db = await open({
        filename: this.dbPath,
        driver: sqlite3.Database
      });

      // 创建交易表
      await this.db.exec(`
        CREATE TABLE IF NOT EXISTS transactions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          signature TEXT UNIQUE NOT NULL,
          timestamp TEXT NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // 创建Mint表
      await this.db.exec(`
        CREATE TABLE IF NOT EXISTS mints (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          transaction_id INTEGER NOT NULL,
          mint_address TEXT NOT NULL,
          FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE
        )
      `);

      // 创建地址查找表
      await this.db.exec(`
        CREATE TABLE IF NOT EXISTS lookup_tables (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          transaction_id INTEGER NOT NULL,
          table_address TEXT NOT NULL,
          table_index INTEGER NOT NULL,
          FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE
        )
      `);

      // 创建Market表
      await this.db.exec(`
        CREATE TABLE IF NOT EXISTS markets (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          transaction_id INTEGER NOT NULL,
          protocol TEXT NOT NULL,
          market_address TEXT NOT NULL,
          program_id TEXT NOT NULL,
          FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE
        )
      `);

      console.log('数据库初始化完成');
    } catch (error) {
      console.error('初始化数据库失败:', error);
      throw error;
    }
  }

  /**
   * 保存交易数据
   */
  async saveTransaction(data: {
    signature: string;
    timestamp: string;
    mints: string[];
    lookupTables: Array<{ tableAddress: string; tableIndex: number }>;
    markets: Array<{ protocol: string; address: string; programId: string }>;
  }): Promise<void> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    try {
      // 开始事务
      await this.db.exec('BEGIN TRANSACTION');

      // 插入交易记录
      const result = await this.db.run(
        'INSERT INTO transactions (signature, timestamp) VALUES (?, ?)',
        [data.signature, data.timestamp]
      );
      const transactionId = result.lastID;

      // 插入Mint信息
      for (const mintAddress of data.mints) {
        await this.db.run(
          'INSERT INTO mints (transaction_id, mint_address) VALUES (?, ?)',
          [transactionId, mintAddress]
        );
      }

      // 插入地址查找表信息
      for (const table of data.lookupTables) {
        await this.db.run(
          'INSERT INTO lookup_tables (transaction_id, table_address, table_index) VALUES (?, ?, ?)',
          [transactionId, table.tableAddress, table.tableIndex]
        );
      }

      // 插入Market信息
      for (const market of data.markets) {
        await this.db.run(
          'INSERT INTO markets (transaction_id, protocol, market_address, program_id) VALUES (?, ?, ?, ?)',
          [transactionId, market.protocol, market.address, market.programId]
        );
      }

      // 删除旧记录，只保留最新的10条
      await this.db.run(`
        DELETE FROM transactions WHERE id NOT IN (
          SELECT id FROM transactions ORDER BY created_at DESC LIMIT ${this.maxRecords}
        )
      `);

      // 提交事务
      await this.db.exec('COMMIT');
      console.log(`交易数据 ${data.signature} 已保存到数据库`);
    } catch (error) {
      // 回滚事务
      await this.db.exec('ROLLBACK');
      console.error('保存交易数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取最新的交易记录
   */
  async getLatestTransactions(): Promise<any[]> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    try {
      const transactions = await this.db.all(`
        SELECT 
          t.id, 
          t.signature, 
          t.timestamp, 
          t.created_at
        FROM 
          transactions t
        ORDER BY 
          t.created_at DESC
        LIMIT ${this.maxRecords}
      `);

      // 为每个交易获取相关数据
      for (const tx of transactions) {
        // 获取Mint信息
        tx.mints = await this.db.all(
          'SELECT mint_address FROM mints WHERE transaction_id = ?',
          [tx.id]
        );

        // 获取地址查找表信息
        tx.lookupTables = await this.db.all(
          'SELECT table_address, table_index FROM lookup_tables WHERE transaction_id = ? ORDER BY table_index',
          [tx.id]
        );

        // 获取Market信息
        tx.markets = await this.db.all(
          'SELECT protocol, market_address, program_id FROM markets WHERE transaction_id = ?',
          [tx.id]
        );
      }

      return transactions;
    } catch (error) {
      console.error('获取交易记录失败:', error);
      throw error;
    }
  }

  /**
   * 根据Mint地址查询交易
   */
  async findTransactionsByMint(mintAddress: string): Promise<any[]> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    try {
      return await this.db.all(`
        SELECT 
          t.id, 
          t.signature, 
          t.timestamp, 
          t.created_at
        FROM 
          transactions t
        JOIN 
          mints m ON t.id = m.transaction_id
        WHERE 
          m.mint_address = ?
        ORDER BY 
          t.created_at DESC
      `, [mintAddress]);
    } catch (error) {
      console.error('查询交易记录失败:', error);
      throw error;
    }
  }

  /**
   * 根据Market地址查询交易
   */
  async findTransactionsByMarket(marketAddress: string): Promise<any[]> {
    if (!this.db) {
      throw new Error('数据库未初始化');
    }

    try {
      return await this.db.all(`
        SELECT 
          t.id, 
          t.signature, 
          t.timestamp, 
          t.created_at
        FROM 
          transactions t
        JOIN 
          markets m ON t.id = m.transaction_id
        WHERE 
          m.market_address = ?
        ORDER BY 
          t.created_at DESC
      `, [marketAddress]);
    } catch (error) {
      console.error('查询交易记录失败:', error);
      throw error;
    }
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    if (this.db) {
      await this.db.close();
      this.db = null;
    }
  }
}
