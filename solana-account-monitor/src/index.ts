import { 
  Connection, 
  PublicKey, 
  ConfirmedSignatureInfo,
  ParsedTransactionWithMeta,
  AddressLookupTableAccount
} from '@solana/web3.js';
import { TOKEN_PROGRAM_ID } from '@solana/spl-token';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';
import { log } from 'console';
import { TransactionDatabase } from './database';

// 加载环境变量
// 使用绝对路径加载 .env 文件
const envPath = path.resolve(__dirname, '../.env');
const result = dotenv.config({ path: envPath });

// 配置
const config = {
  // 确保始终有一个有效的RPC端点
  rpcEndpoint: process.env.RPC_ENDPOINT || 'http://************:8899',
  monitoredAddress: process.env.MONITORED_ADDRESS || '3ZNcUhGgmxiPF2bUMEw2a6hhFMWWdFMrsAotXtZYirGE',
  historyLimit: parseInt(process.env.HISTORY_LIMIT || '10'),
  pollingInterval: parseInt(process.env.POLLING_INTERVAL || '10000'), // 10秒
  shyftApiKey: process.env.SHYFT_API_KEY || 'wl1pw-E6N71CmH_C', // 使用您之前提供的API密钥
  dataDir: process.env.DATA_DIR || './data' // 本地数据保存目录
};

// 主要类
class SolanaAccountMonitor {
  private connection: Connection;
  private monitoredAddress: PublicKey;
  private processedSignatures: Set<string> = new Set();
  private isRunning: boolean = false;
  private dataDir: string;
  private db: TransactionDatabase;

  constructor(rpcEndpoint: string, monitoredAddress: string) {
    this.connection = new Connection(rpcEndpoint);
    this.monitoredAddress = new PublicKey(monitoredAddress);
    this.dataDir = config.dataDir;
    
    // 确保数据目录存在
    this.ensureDataDirExists();
    
    // 初始化数据库
    this.db = new TransactionDatabase(this.dataDir);
    
    console.log(`初始化监控 - RPC: ${rpcEndpoint}`);
    console.log(`监控地址: 'https://solscan.io/account/${monitoredAddress}#defiactivities`);
    console.log(`数据保存目录: ${this.dataDir}`);
  }
  
  // 确保数据目录存在
  private ensureDataDirExists(): void {
    try {
      if (!fs.existsSync(this.dataDir)) {
        fs.mkdirSync(this.dataDir, { recursive: true });
        console.log(`创建数据目录: ${this.dataDir}`);
      }
    } catch (error) {
      console.error('创建数据目录时出错:', error);
    }
  }

  // 开始监控
  public async start(): Promise<void> {
    if (this.isRunning) {
      console.log('监控已经在运行中');
      return;
    }

    this.isRunning = true;
    console.log('开始监控账户活动...');
    
    // 初始化数据库
    await this.db.initialize();
    console.log('数据库已初始化');

    // 首先获取历史交易
    await this.fetchHistoricalTransactions();

    // 然后开始轮询新交易
    this.pollForNewTransactions();
  }

  // 停止监控
  public async stop(): Promise<void> {
    this.isRunning = false;
    console.log('停止监控');
    
    // 关闭数据库连接
    await this.db.close();
    console.log('数据库连接已关闭');
  }

  // 获取历史交易
  private async fetchHistoricalTransactions(): Promise<void> {
    try {
      console.log(`获取最近 ${config.historyLimit} 条历史交易...`);
      
      const signatures = await this.connection.getSignaturesForAddress(
        this.monitoredAddress,
        { limit: config.historyLimit }
      );

      console.log(`找到 ${signatures.length} 条历史交易`);
      
      // 处理每个交易
      for (const signatureInfo of signatures) {
        await this.processTransaction(signatureInfo);
      }
    } catch (error) {
      console.error('获取历史交易时出错:', error);
    }
  }

  // 轮询新交易
  private pollForNewTransactions(): void {
    if (!this.isRunning) return;

    setTimeout(async () => {
      try {
        // 获取最新的交易签名
        const signatures = await this.connection.getSignaturesForAddress(
          this.monitoredAddress,
          { limit: 5 } // 只获取最新的几条
        );

        // 处理新交易
        for (const signatureInfo of signatures) {
          if (!this.processedSignatures.has(signatureInfo.signature)) {
            await this.processTransaction(signatureInfo);
          }
        }
      } catch (error) {
        console.error('轮询新交易时出错:', error);
      }

      // 继续轮询
      this.pollForNewTransactions();
    }, config.pollingInterval);
  }

  // 检查是否是Jito小费交易
  private isJitoTipTransaction(transaction: ParsedTransactionWithMeta): boolean {
    // 检查交易中是否包含Jito小费程序
    const jitoTipProgramId = 'TipgrjcESvvR7G4MUDiR1dWgbFqC6fprUPyZeYVDqFS';
    
    // 检查指令中是否有Jito小费程序
    const hasJitoProgram = transaction.transaction.message.instructions.some(
      instruction => 'programId' in instruction && instruction.programId.toString() === jitoTipProgramId
    );
    
    if (hasJitoProgram) {
      console.log('检测到Jito小费交易，将被过滤');
      return true;
    }

    // 检查交易版本
    const isLegacyVersion = transaction.version === 'legacy';
    if (isLegacyVersion) {
      console.log('检测到 legacy 版本交易，将被过滤');
      return true;
    }
    
    return false;
  }
  
  // 处理单个交易
  private async processTransaction(signatureInfo: ConfirmedSignatureInfo): Promise<void> {
    try {
      const signature = signatureInfo.signature;
      
      // 如果已经处理过，跳过
      if (this.processedSignatures.has(signature)) {
        return;
      }

      console.log(`处理交易: ${signature}`);
      
      // 获取交易详情
      const transaction = await this.connection.getParsedTransaction(
        signature,
        { maxSupportedTransactionVersion: 0 }
      );

      if (!transaction) {
        console.log(`无法获取交易详情: ${signature}`);
        return;
      }
      
      // 检查是否是Jito小费交易，如果是则跳过
      if (this.isJitoTipTransaction(transaction)) {
        console.log(`跳过Jito小费交易: ${signature}`);
        // 仍然标记为已处理，避免重复处理
        this.processedSignatures.add(signature);
        return;
      }

      // 标记为已处理
      this.processedSignatures.add(signature);

      // 解析交易信息
      await this.parseTransactionDetails(transaction, signature);
    } catch (error) {
      console.error(`处理交易 ${signatureInfo.signature} 时出错:`, error);
    }
  }

  // 提取地址查找表信息
  private extractAddressLookupTables(txn: ParsedTransactionWithMeta) {
    if (txn.transaction.message.addressTableLookups) {
      return txn.transaction.message.addressTableLookups.map(lookup => ({
        tableAddress: lookup.accountKey.toString(),
        writableIndexes: lookup.writableIndexes,
        readonlyIndexes: lookup.readonlyIndexes
      }));
    }
    return [];
  }
  
  // 提取Market信息
  private extractMarketInfo(txn: ParsedTransactionWithMeta): Array<{protocol: string, address: string, programId?: string}> {
    const markets: Array<{protocol: string, address: string, programId?: string}> = [];
    const meteoraDlmmProgramId = 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo';
    const pumpAmmProgramId = 'pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA'; // Pump AMM 程序 ID
    // 需要过滤的地址列表
    const excludedAddresses = [
      'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6',
      'GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR'
    ];
    
    try {
      // 检查交易中的所有指令
      if (txn.transaction.message.instructions) {
        txn.transaction.message.instructions.forEach(instruction => {
          // 处理 PartiallyDecodedInstruction 类型
          if ('programId' in instruction) {
            const programIdStr = instruction.programId.toString();
            
            // 处理 Meteora DLMM 程序
            if (programIdStr === meteoraDlmmProgramId && 
                'accounts' in instruction && 
                instruction.accounts && 
                instruction.accounts.length > 0) {
              
              const address = instruction.accounts[0].toString();
              // 过滤排除的地址
              if (!excludedAddresses.includes(address)) {
                // Meteora DLMM 的 Market 地址通常在第一个位置
                markets.push({
                  protocol: 'Meteora DLMM',
                  address: address,
                  programId: meteoraDlmmProgramId
                });
              }
            }
            
            // 处理 Pump AMM 程序
            else if (programIdStr === pumpAmmProgramId && 
                'accounts' in instruction && 
                instruction.accounts && 
                instruction.accounts.length > 0) {
              
              const address = instruction.accounts[0].toString();
              // 过滤排除的地址
              if (!excludedAddresses.includes(address)) {
                // Pump AMM 的 Market 地址在第一个位置
                markets.push({
                  protocol: 'Pump AMM',
                  address: address,
                  programId: pumpAmmProgramId
                });
              }
            }
          }
        });
      }
      
      // 检查内部指令
      if (txn.meta && txn.meta.innerInstructions) {
        txn.meta.innerInstructions.forEach(innerIx => {
          if (innerIx.instructions) {
            innerIx.instructions.forEach(instruction => {
              // 处理 PartiallyDecodedInstruction 类型
              if ('programId' in instruction) {
                const programIdStr = instruction.programId.toString();
                
                // 处理 Meteora DLMM 程序
                if (programIdStr === meteoraDlmmProgramId && 
                    'accounts' in instruction && 
                    instruction.accounts && 
                    instruction.accounts.length > 0) {
                  
                  const address = instruction.accounts[0].toString();
                  if (!excludedAddresses.includes(address) && !markets.some(market => market.address === address)) {
                    markets.push({
                      protocol: 'Meteora DLMM',
                      address: address,
                      programId: meteoraDlmmProgramId
                    });
                  }
                }
                
                // 处理 Pump AMM 程序
                else if (programIdStr === pumpAmmProgramId && 
                    'accounts' in instruction && 
                    instruction.accounts && 
                    instruction.accounts.length > 0) {
                  
                  const address = instruction.accounts[0].toString();
                  if (!excludedAddresses.includes(address) && !markets.some(market => market.address === address)) {
                    markets.push({
                      protocol: 'Pump AMM',
                      address: address,
                      programId: pumpAmmProgramId
                    });
                  }
                }
              }
            });
          }
        });
      }
      
      return markets;
    } catch (error) {
      console.error('提取Market信息时出错:', error);
      return [];
    }
  }
  
  // 从交易中提取mint信息
  private extractMintInfo(txn: ParsedTransactionWithMeta): string[] {
    const preTokenBalances = txn.meta?.preTokenBalances || [];
    
    // 只提取mint地址
    const mintAddresses: string[] = [];
    
    // 直接使用 preTokenBalances 中的第一个 mint
    if (preTokenBalances.length > 0) {
      const firstBalance = preTokenBalances[0];
      if (firstBalance.mint) {
        mintAddresses.push(firstBalance.mint);
      }
    }
    
    return mintAddresses;
  }

  // 将交易数据保存到本地文件
  private saveTransactionToFile(transaction: ParsedTransactionWithMeta, signature: string): void {
    try {
      // 创建交易数据子目录
      const txnDir = path.join(this.dataDir, 'transactions');
      if (!fs.existsSync(txnDir)) {
        fs.mkdirSync(txnDir, { recursive: true });
      }
      
      // 创建时间戳
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      
      // 保存完整交易数据
      const filename = path.join(txnDir, `${timestamp}_${signature}.json`);
      fs.writeFileSync(filename, JSON.stringify(transaction, null, 2));
      console.log(`交易数据已保存到: ${filename}`);
    } catch (error) {
      console.error(`保存交易数据到文件时出错:`, error);
    }
  }
  
  // 解析交易详情
  private async parseTransactionDetails(
    transaction: ParsedTransactionWithMeta,
    signature: string
  ): Promise<void> {
    try {
      // 保存交易数据到本地文件
      this.saveTransactionToFile(transaction, signature);
      console.log(`\n===== 交易详情 ${signature} =====`);
      
      // 打印整个交易对象以便调试
      // console.log('交易对象:');
      // console.log(JSON.stringify(transaction, null, 2));
      
      // 1. 获取地址查找表信息
      const lookupTables = this.extractAddressLookupTables(transaction);
      
      if (lookupTables.length > 0) {
        console.log('地址查找表信息:');
        lookupTables.forEach((table, index) => {
          console.log(`  表 ${index + 1} - 地址: ${table.tableAddress}`);
          // console.log(`    可写索引: ${table.writableIndexes.join(', ')}`);
          // console.log(`    只读索引: ${table.readonlyIndexes.join(', ')}`);
        });
      } else {
        console.log('  没有使用地址查找表');
      }
      
      // 2. 提取交易中的Mint信息
      const mintAddresses = this.extractMintInfo(transaction);
      
      if (mintAddresses.length > 0) {
        console.log('\nMint信息:');
        console.log(`  发现 ${mintAddresses.length} 个Mint地址:`);
        
        for (const mintAddress of mintAddresses) {
          console.log(`  - Mint地址: ${mintAddress}`);
        }
      } else {
        console.log('\n没有找到Mint信息');
      }

      // 3. 获取market信息
      const marketInfo = this.extractMarketInfo(transaction);
      if (marketInfo.length > 0) {
        console.log('\nMarket信息:');
        marketInfo.forEach((market, index) => {
          console.log(`  Market ${index + 1}:`);
          console.log(`    协议: ${market.protocol}`);
          console.log(`    地址: ${market.address}`);
          if (market.programId) {
            console.log(`    程序ID: ${market.programId}`);
          }
        });
      } else {
        console.log('\n未找到Market信息');
      }
      
      // 4. 将交易数据保存到数据库
      try {
        // 准备地址查找表数据
        const formattedLookupTables = lookupTables.map((table, index) => ({
          tableAddress: table.tableAddress,
          tableIndex: index
        }));
        
        // 准备市场数据，确保programId存在
        const formattedMarkets = marketInfo.map(market => ({
          protocol: market.protocol,
          address: market.address,
          programId: market.programId || 'unknown'
        }));
        
        // 保存到数据库
        await this.db.saveTransaction({
          signature: signature,
          timestamp: transaction.blockTime ? new Date(transaction.blockTime * 1000).toISOString() : new Date().toISOString(),
          mints: mintAddresses,
          lookupTables: formattedLookupTables,
          markets: formattedMarkets
        });
        
        console.log(`交易数据已保存到数据库`);
      } catch (dbError) {
        console.error('保存交易到数据库时出错:', dbError);
      }
      
      console.log(`===== 交易详情结束 =====\n`);
    } catch (error) {
      console.error(`解析交易详情时出错:`, error);
    }
  }

}

// 主函数
async function main() {
  // 检查配置
  if (!config.monitoredAddress) {
    console.error('错误: 未设置监控地址。请在.env文件中设置MONITORED_ADDRESS环境变量。');
    process.exit(1);
  }

  // 创建并启动监控器
  const monitor = new SolanaAccountMonitor(
    config.rpcEndpoint,
    config.monitoredAddress
  );

  // 处理退出信号
  process.on('SIGINT', async () => {
    console.log('\n接收到退出信号');
    await monitor.stop();
    process.exit(0);
  });

  // 启动监控
  await monitor.start();
}

// 运行主函数
main().catch(error => {
  console.error('程序运行出错:', error);
  process.exit(1);
});
