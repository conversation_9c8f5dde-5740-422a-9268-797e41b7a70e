# Node.js依赖目录
node_modules/

# 日志文件
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 目录用于存储工具的缓存
.npm
.eslintcache
.node_repl_history
.yarn-integrity

# 环境变量文件（包含敏感信息）
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 构建输出
dist/
build/
out/

# 覆盖率输出
coverage/

# TypeScript缓存
*.tsbuildinfo

# 编辑器目录和文件
.idea/
.vscode/
*.swp
*.swo
.DS_Store
